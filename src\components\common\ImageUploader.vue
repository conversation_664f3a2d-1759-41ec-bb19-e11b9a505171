<template>
  <div class="image-uploader">
    <!-- 이미지 업로드 영역 -->
    <div
      class="upload-area"
      :class="{ 'drag-over': isDragging }"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
      @click="triggerFileInput"
    >
      <input
        type="file"
        ref="fileInput"
        class="file-input"
        accept="image/*"
        @change="handleFileChange"
      />

      <div v-if="!isUploading && !previewUrl" class="upload-placeholder">
        <div class="upload-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="17 8 12 3 7 8"></polyline>
            <line x1="12" y1="3" x2="12" y2="15"></line>
          </svg>
        </div>
        <p class="upload-text">이미지를 드래그하거나 클릭하여 업로드</p>
        <p class="upload-hint">JPG, PNG, GIF 파일 (최대 5MB)</p>
      </div>

      <div v-else-if="isUploading" class="upload-loading">
        <div class="spinner"></div>
        <p>업로드 중... {{ uploadProgress }}%</p>
      </div>

      <div v-else class="preview-container">
        <img :src="previewUrl" alt="이미지 미리보기" class="preview-image" />
        <div class="preview-overlay">
          <button type="button" class="remove-btn" @click.stop="removeImage">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 에러 메시지 -->
    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <!-- 이미지 갤러리 (선택 사항) -->
    <div v-if="showGallery && imageList.length > 0" class="image-gallery">
      <h4>최근 업로드한 이미지</h4>
      <div class="gallery-grid">
        <div
          v-for="image in imageList"
          :key="image.id"
          class="gallery-item"
          :class="{ 'selected': selectedImageUrl === image.url }"
          @click="selectImage(image)"
        >
          <img :src="image.url" :alt="image.name || '이미지'" class="gallery-image" />
          <div class="gallery-overlay">
            <button type="button" class="select-btn" @click.stop="selectImage(image)">선택</button>
            <button type="button" class="delete-btn" @click.stop="confirmDeleteImage(image)">삭제</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 삭제 확인 모달 -->
    <div v-if="showDeleteModal" class="modal-overlay" @click="cancelDelete">
      <div class="modal-content" @click.stop>
        <h3>이미지 삭제 확인</h3>
        <p>이 이미지를 삭제하시겠습니까?</p>
        <p class="warning">이 작업은 되돌릴 수 없습니다.</p>
        <div class="modal-actions">
          <button @click="deleteSelectedImage" class="confirm-btn" :disabled="isDeleting">
            {{ isDeleting ? '삭제 중...' : '삭제' }}
          </button>
          <button @click="cancelDelete" class="cancel-btn">취소</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { uploadImage, getImages, deleteImage } from '@/api/upload';

// 프롭스
const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'landing'
  },
  showGallery: {
    type: Boolean,
    default: true
  },
  maxSize: {
    type: Number,
    default: 5 * 1024 * 1024 // 5MB
  }
});

// 이벤트
const emit = defineEmits(['input', 'change', 'upload-success', 'upload-error']);

// 스토어
const authStore = useAuthStore();

// 상태
const fileInput = ref(null);
const isDragging = ref(false);
const isUploading = ref(false);
const uploadProgress = ref(0);
const previewUrl = ref('');
const error = ref('');
const imageList = ref([]);
const isLoading = ref(false);
const selectedImageUrl = ref(props.value);
const showDeleteModal = ref(false);
const imageToDelete = ref(null);
const isDeleting = ref(false);

// 현재 프로젝트 ID
const currentProjectId = computed(() => {
  return authStore.currentProject?.projectId;
});

// 초기 값 설정
watch(() => props.value, (newValue) => {
  if (newValue) {
    previewUrl.value = newValue;
    selectedImageUrl.value = newValue;
  } else {
    previewUrl.value = '';
    selectedImageUrl.value = '';
  }
});

// 파일 입력 트리거
const triggerFileInput = () => {
  if (!isUploading.value) {
    fileInput.value.click();
  }
};

// 드래그 오버 핸들러
const handleDragOver = (event) => {
  isDragging.value = true;
};

// 드래그 리브 핸들러
const handleDragLeave = (event) => {
  isDragging.value = false;
};

// 드롭 핸들러
const handleDrop = (event) => {
  isDragging.value = false;

  const files = event.dataTransfer.files;
  if (files.length > 0) {
    handleFile(files[0]);
  }
};

// 파일 변경 핸들러
const handleFileChange = (event) => {
  const files = event.target.files;
  if (files.length > 0) {
    handleFile(files[0]);
  }
};

// 파일 처리
const handleFile = (file) => {
  // 파일 유형 검사
  if (!file.type.match('image.*')) {
    error.value = '이미지 파일만 업로드할 수 있습니다.';
    return;
  }

  // 파일 크기 검사
  if (file.size > props.maxSize) {
    error.value = `파일 크기는 ${props.maxSize / (1024 * 1024)}MB 이하여야 합니다.`;
    return;
  }

  error.value = '';

  // 파일 미리보기
  const reader = new FileReader();
  reader.onload = (e) => {
    previewUrl.value = e.target.result;
  };
  reader.readAsDataURL(file);

  // 파일 업로드
  uploadFile(file);
};

// 파일 업로드
const uploadFile = async (file) => {
  isUploading.value = true;
  uploadProgress.value = 0;

  try {
    // 진행 상태 시뮬레이션 (실제로는 서버에서 진행 상태를 받아야 함)
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10;
      }
    }, 300);

    // 이미지 업로드 API 호출
    const result = await uploadImage(file, props.type, currentProjectId.value);

    clearInterval(progressInterval);
    uploadProgress.value = 100;

    // 이미지 URL 확인
    if (result && result.imageUrl) {
      // 이미지 URL 설정
      previewUrl.value = result.imageUrl;
      selectedImageUrl.value = result.imageUrl;

      // 이벤트 발생
      emit('input', result.imageUrl);
      emit('change', result.imageUrl);
      emit('upload-success', result);

      // 이미지 목록 새로고침
      loadImages();
    } else {
      throw new Error('이미지 URL을 받지 못했습니다. 서버 응답: ' + JSON.stringify(result));
    }
  } catch (err) {
    error.value = err.message || '이미지 업로드 중 오류가 발생했습니다.';
    emit('upload-error', err);
  } finally {
    isUploading.value = false;
  }
};

// 이미지 제거
const removeImage = () => {
  previewUrl.value = '';
  selectedImageUrl.value = '';

  // 파일 입력 초기화
  if (fileInput.value) {
    fileInput.value.value = '';
  }

  // 이벤트 발생
  emit('input', '');
  emit('change', '');
};

// 이미지 목록 로드
const loadImages = async () => {
  if (!props.showGallery) return;

  isLoading.value = true;

  try {
    // QR 코드 이미지 목록 API와 동일한 응답 구조 처리
    const response = await getImages(props.type, currentProjectId.value);

    // 이미지 목록 처리
    if (response && Array.isArray(response)) {
      // 이미지 목록 데이터 구조 변환
      imageList.value = response.map(img => ({
        id: img.id || img.imageId,
        url: img.imageUrl,
        name: img.imageName || img.name || '이미지',
        type: img.type || props.type,
        createDate: img.createDate
      }));
    } else {
      imageList.value = [];
    }
  } catch (err) {
    console.error('이미지 목록 로드 오류:', err);
    error.value = '이미지 목록을 불러오는 중 오류가 발생했습니다.';
    imageList.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 이미지 선택
const selectImage = (image) => {
  selectedImageUrl.value = image.url;
  previewUrl.value = image.url;

  // 이벤트 발생
  emit('input', image.url);
  emit('change', image.url);
};

// 이미지 삭제 확인
const confirmDeleteImage = (image) => {
  imageToDelete.value = image;
  showDeleteModal.value = true;
};

// 이미지 삭제
const deleteSelectedImage = async () => {
  if (!imageToDelete.value) return;

  isDeleting.value = true;

  try {
    await deleteImage(imageToDelete.value.id);

    // 이미지 목록에서 제거
    imageList.value = imageList.value.filter(img => img.id !== imageToDelete.value.id);

    // 선택된 이미지가 삭제된 이미지인 경우 선택 해제
    if (selectedImageUrl.value === imageToDelete.value.url) {
      removeImage();
    }

    // 모달 닫기
    showDeleteModal.value = false;
    imageToDelete.value = null;
  } catch (err) {
    console.error('이미지 삭제 오류:', err);
    error.value = '이미지 삭제 중 오류가 발생했습니다.';
  } finally {
    isDeleting.value = false;
  }
};

// 삭제 취소
const cancelDelete = () => {
  showDeleteModal.value = false;
  imageToDelete.value = null;
};

// 컴포넌트 마운트 시 이미지 목록 로드
onMounted(() => {
  if (props.value) {
    previewUrl.value = props.value;
    selectedImageUrl.value = props.value;
  }

  loadImages();
});
</script>

<style scoped>
.image-uploader {
  width: 100%;
}

.upload-area {
  position: relative;
  width: 100%;
  height: 200px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.upload-area:hover {
  border-color: #2196F3;
  background-color: rgba(33, 150, 243, 0.05);
}

.drag-over {
  border-color: #2196F3;
  background-color: rgba(33, 150, 243, 0.1);
}

.file-input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  text-align: center;
}

.upload-icon {
  color: #999;
  margin-bottom: 10px;
}

.upload-text {
  margin: 0 0 5px;
  font-size: 14px;
  color: #666;
}

.upload-hint {
  margin: 0;
  font-size: 12px;
  color: #999;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(33, 150, 243, 0.3);
  border-radius: 50%;
  border-top-color: #2196F3;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.preview-overlay {
  position: absolute;
  top: 0;
  right: 0;
  padding: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.preview-container:hover .preview-overlay {
  opacity: 1;
}

.remove-btn {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  padding: 0;
}

.remove-btn:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.error-message {
  color: #f44336;
  font-size: 12px;
  margin-top: 8px;
}

.image-gallery {
  margin-top: 20px;
}

.image-gallery h4 {
  font-size: 14px;
  margin-top: 0;
  margin-bottom: 10px;
  color: #555;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
}

.gallery-item {
  position: relative;
  width: 100%;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
}

.gallery-item.selected {
  border-color: #2196F3;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.select-btn, .delete-btn {
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  margin: 2px;
  font-size: 11px;
  cursor: pointer;
}

.select-btn {
  color: #2196F3;
}

.delete-btn {
  color: #f44336;
}

.select-btn:hover, .delete-btn:hover {
  background-color: white;
}

/* 모달 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 300px;
  max-width: 90%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
  margin-top: 0;
  color: #333;
}

.warning {
  color: #f44336;
  font-weight: bold;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.confirm-btn, .cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-btn {
  background-color: #F44336;
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background-color: #d32f2f;
}

.confirm-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.cancel-btn {
  background-color: #e0e0e0;
  color: #333;
}

.cancel-btn:hover {
  background-color: #d0d0d0;
}
</style>
