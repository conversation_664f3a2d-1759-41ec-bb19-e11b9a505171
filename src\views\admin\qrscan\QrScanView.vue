<template>
  <div class="qr-scan-view">
    <h1>QR 코드 스캔</h1>

    <!-- 스캔 모드 선택 -->
    <div class="scan-mode-selector">
      <button
        @click="changeScanMode('attendance')"
        class="mode-btn"
        :class="{ 'active': scanMode === 'attendance' }"
      >
        참석 처리
      </button>
      <button
        @click="changeScanMode('redemption')"
        class="mode-btn"
        :class="{ 'active': scanMode === 'redemption' }"
      >
        혜택 처리
      </button>
    </div>

    <!-- 현재 모드 표시 -->
    <div class="current-mode">
      <p>현재 모드: <strong>{{ scanMode === 'attendance' ? '참석 처리' : '혜택 처리' }}</strong></p>
    </div>

    <!-- 카메라 접근 권한 요청 및 안내 메시지 -->
    <div v-if="!cameraPermission" class="camera-permission">
      <p>카메라 접근 권한이 필요합니다.</p>
      <p v-if="!isCameraSupported" class="browser-warning">
        이 브라우저는 카메라 접근을 지원하지 않거나 HTTPS 환경에서 실행되지 않습니다.<br>
        Chrome, Firefox, Safari 등의 최신 브라우저를 사용하고 HTTPS 환경에서 실행해주세요.
      </p>
      <button @click="requestCameraPermission" class="permission-btn" :disabled="!isCameraSupported">카메라 권한 요청</button>
    </div>

    <!-- 카메라 뷰 영역 -->
    <div v-else class="scanner-container">
      <div id="qr-reader" ref="qrReader" class="qr-reader-container"></div>

      <!-- 스캔 컨트롤 -->
      <div class="scanner-controls">
        <button @click="toggleScanner" class="control-btn" :class="{ 'scan-btn': !isScanning, 'stop-btn': isScanning, 'rescan-btn': scanResult }">
          {{ isScanning ? '스캔 중지' : (scanResult ? '다시 스캔하기' : '스캔 시작') }}
        </button>
        <button @click="switchCamera" class="control-btn" :disabled="isScanning">카메라 전환</button>
      </div>

      <!-- 디버그 정보 -->
      <div class="debug-info">
        <p><strong>카메라 지원:</strong> {{ isCameraSupported ? '지원됨' : '지원되지 않음' }}</p>
        <p><strong>현재 카메라 모드:</strong> {{ currentCamera }}</p>
        <p><strong>현재 카메라 ID:</strong> {{ currentCameraId || '없음' }}</p>
        <p><strong>스캔 상태:</strong> {{ isScanning ? '스캔 중' : '중지됨' }}</p>
      </div>
    </div>

    <!-- 스캔 결과 표시 영역 -->
    <div class="scan-result" :class="resultClass">
      <div v-if="scanResult" class="result-content">
        <h3>스캔 결과</h3>
        <p class="result-message">{{ scanResult.message }}</p>
      </div>
    </div>

    <!-- 스캔 이력 -->
    <div class="scan-history">
      <div class="history-header">
        <h3>최근 스캔 이력</h3>
        <button v-if="scanHistory.length > 0" @click="clearScanHistory" class="clear-btn">전체 삭제</button>
      </div>
      <div v-if="scanHistory.length > 0" class="history-groups">
        <!-- 날짜별 그룹 -->
        <div v-for="group in groupedScanHistory" :key="group.date" class="history-group">
          <div class="date-header">{{ group.date }}</div>
          <ul class="history-items">
            <li v-for="(item, itemIndex) in group.items" :key="itemIndex" :class="{ success: item.success }">
              <span class="time">{{ formatTime(item.timestamp) }}</span>
              <span class="code">{{ item.code }}</span>
              <span class="status">{{ item.success ? '성공' : '실패' }}</span>
              <span class="message">{{ item.message }}</span>
              <!-- 항목 인덱스 계산 -->
              <button @click="deleteHistoryItem(scanHistory.findIndex(historyItem => historyItem === item))" class="delete-btn">삭제</button>
            </li>
          </ul>
        </div>
      </div>
      <p v-else>스캔 이력이 없습니다.</p>
    </div>

    <!-- 참가자 혜택 정보 모달 -->
    <div v-if="showRedemptionModal" class="redemption-modal-overlay">
      <div class="redemption-modal">
        <div class="redemption-modal-header">
          <h3>참가자 혜택 정보</h3>
          <button class="close-btn" @click="closeRedemptionModal" title="닫기">&times;</button>
        </div>
        <div class="redemption-modal-content">
          <div v-if="redemptionDetails" class="attendee-info">
            <div class="info-row">
              <span class="info-label">이벤트 이름:</span>
              <span class="info-value">{{ redemptionDetails.eventName }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">참가자 이름:</span>
              <span class="info-value">{{ redemptionDetails.attendeeName }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">확인 코드:</span>
              <span class="info-value">{{ redemptionDetails.confirmationCode }}</span>
            </div>
            <div class="info-row" v-if="redemptionDetails.attendeeEmail">
              <span class="info-label">이메일:</span>
              <span class="info-value">{{ redemptionDetails.attendeeEmail }}</span>
            </div>
          </div>

          <div v-if="redemptionDetails && redemptionDetails.benefits && redemptionDetails.benefits.length > 0" class="benefits-list">
            <h4>혜택 목록</h4>
            <table class="benefits-table">
              <thead>
                <tr>
                  <th>혜택 이름</th>
                  <th>사용 여부</th>
                  <th>사용 시각</th>
                  <th>기능</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="benefit in redemptionDetails.benefits" :key="benefit.benefitId">
                  <td>{{ benefit.benefitName }}</td>
                  <td>
                    <span class="status-badge" :class="benefit.redeemed ? 'status-redeemed' : 'status-not-redeemed'">
                      {{ benefit.redeemed ? '사용됨' : '미사용' }}
                    </span>
                  </td>
                  <td>{{ benefit.redeemedAt ? formatDateTime(benefit.redeemedAt) : '-' }}</td>
                  <td>
                    <div class="action-buttons">
                      <!-- 사용 처리 버튼 -->
                      <button
                        v-if="!benefit.redeemed"
                        @click="processBenefitRedemption(redemptionDetails.confirmationCode, benefit.benefitId)"
                        class="redeem-btn"
                      >
                        사용 처리
                      </button>

                      <!-- 사용 완료 표시 및 취소 버튼 -->
                      <div v-else class="redeemed-actions">
                        <button
                          @click="cancelBenefitRedemptionProcess(redemptionDetails.confirmationCode, benefit.benefitId)"
                          class="cancel-btn"
                          title="혜택 사용 취소"
                        >
                          취소
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div v-else-if="redemptionDetails" class="no-benefits">
            <p>등록된 혜택이 없습니다.</p>
          </div>
          <div v-else class="loading-benefits">
            <p>혜택 정보를 불러오는 중...</p>
          </div>
        </div>
        <div class="redemption-modal-footer">
          <button class="control-btn close-modal-btn" @click="closeRedemptionModal">모달 닫기</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, onBeforeUnmount, computed, watch, nextTick } from 'vue';
import { onBeforeRouteLeave } from 'vue-router';
import { Html5Qrcode, Html5QrcodeSupportedFormats } from 'html5-qrcode';
import { processAttendance, getAttendeeRedemptionDetails, redeemBenefit, cancelBenefitRedemption } from '@/api/attendees';
import { useAuthStore } from '@/stores/auth';
import { format, parseISO } from 'date-fns';

// 상태 관리
const authStore = useAuthStore();
const qrReader = ref(null);
const html5QrCode = ref(null);
const isScanning = ref(false);
const cameraPermission = ref(false);
const scanResult = ref(null);
const scanHistory = ref([]);
const currentCamera = ref('environment'); // 'environment' (후면) 또는 'user' (전면)
const currentCameraId = ref(''); // 현재 사용 중인 카메라 ID

// 스캔 모드 관리 ('attendance': 참석 처리, 'redemption': 혜택 처리)
const scanMode = ref('attendance');

// 혜택 정보 모달 관련 상태
const showRedemptionModal = ref(false);
const redemptionDetails = ref(null);

// 사용자 ID 가져오기 (로그인한 사용자의 이메일 또는 ID)
const userId = computed(() => {
  return authStore.user?.userEmail || 'anonymous';
});

// localStorage 키 생성 (사용자별, 모드별 구분)
const storageKey = computed(() => {
  return `way-qr-scan-history-${userId.value}-${scanMode.value}`;
});

// 브라우저 카메라 지원 여부 확인
const isCameraSupported = ref(false);

// 브라우저 카메라 지원 여부 확인
try {
  isCameraSupported.value = !!(navigator && navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
} catch (error) {
  console.error('브라우저 카메라 지원 확인 오류:', error);
  isCameraSupported.value = false;
}

// localStorage에서 스캔 이력 불러오기
const loadScanHistory = () => {
  try {
    const savedHistory = localStorage.getItem(storageKey.value);
    if (savedHistory) {
      scanHistory.value = JSON.parse(savedHistory);
    }
  } catch (error) {
    console.error('스캔 이력 불러오기 오류:', error);
    scanHistory.value = [];
  }
};

// localStorage에 스캔 이력 저장
const saveScanHistory = () => {
  try {
    // 이력이 비어있으면 해당 항목을 삭제
    if (scanHistory.value.length === 0) {
      localStorage.removeItem(storageKey.value);
    } else {
      // 이력이 있으면 저장
      localStorage.setItem(storageKey.value, JSON.stringify(scanHistory.value));
    }
  } catch (error) {
    console.error('스캔 이력 저장 오류:', error);
  }
};

// 스캔 이력 항목 삭제
const deleteHistoryItem = (index) => {
  // 해당 항목 삭제
  scanHistory.value.splice(index, 1);

  // 이력이 비어있으면 localStorage에서 해당 항목 삭제
  if (scanHistory.value.length === 0) {
    localStorage.removeItem(storageKey.value);
  } else {
    // 이력이 있으면 저장
    localStorage.setItem(storageKey.value, JSON.stringify(scanHistory.value));
  }
};

// 스캔 모드 변경
const changeScanMode = (mode) => {
  // 스캔 중인 경우 중지
  if (isScanning.value) {
    stopScanner();
  }

  // 결과 초기화
  scanResult.value = null;

  // 모달 닫기
  if (showRedemptionModal.value) {
    closeRedemptionModal();
  }

  // 모드 변경
  scanMode.value = mode;

  // 새 모드에 맞는 스캔 이력 로드
  loadScanHistory();
};

// 참가자 혜택 정보 모달 닫기
const closeRedemptionModal = () => {
  showRedemptionModal.value = false;
  redemptionDetails.value = null;
};

// 혜택 사용 처리
const processBenefitRedemption = async (confirmationCode, benefitId) => {
  try {
    // API 호출
    const response = await redeemBenefit(confirmationCode, benefitId);

    // 성공 메시지 표시
    alert('혜택 사용 처리가 완료되었습니다.');

    // 혜택 정보 다시 불러오기 (UI 갱신)
    await fetchAttendeeRedemptionDetails(confirmationCode);

    return true;
  } catch (error) {
    console.error('혜택 사용 처리 실패:', error);

    // 오류 메시지 표시
    alert(error.message || '처리 중 오류가 발생했습니다.');

    return false;
  }
};

// 혜택 사용 취소 처리
const cancelBenefitRedemptionProcess = async (confirmationCode, benefitId) => {
  try {
    // 사용자 확인
    if (!confirm('정말로 이 혜택의 사용 처리를 취소하시겠습니까?')) {
      return false;
    }

    // API 호출
    const response = await cancelBenefitRedemption(confirmationCode, benefitId);

    // 성공 메시지 표시
    alert('혜택 사용이 성공적으로 취소되었습니다.');

    // 혜택 정보 다시 불러오기 (UI 갱신)
    await fetchAttendeeRedemptionDetails(confirmationCode);

    return true;
  } catch (error) {
    console.error('혜택 사용 취소 실패:', error);

    // 오류 메시지 표시
    alert(error.message || '취소 처리 중 오류가 발생했습니다.');

    return false;
  }
};

// 참가자 혜택 정보 가져오기
const fetchAttendeeRedemptionDetails = async (confirmationCode) => {
  try {
    // API 호출
    const response = await getAttendeeRedemptionDetails(confirmationCode);

    // 응답 데이터 설정
    if (response && response.data) {
      redemptionDetails.value = response.data;
      showRedemptionModal.value = true;

      // 스캔 결과 설정
      scanResult.value = {
        success: true,
        message: '혜택 정보를 성공적으로 불러왔습니다.',
        attendeeName: response.data.attendeeName || '참가자'
      };

      // 스캔 이력에 추가
      const now = new Date();
      scanHistory.value.unshift({
        code: confirmationCode,
        success: true,
        message: `${response.data.attendeeName || '참가자'}의 혜택 정보 조회 성공`,
        timestamp: now.getTime(),
        dateString: formatDate(now)
      });

      // 이력 최대 10개 유지
      if (scanHistory.value.length > 10) {
        scanHistory.value.pop();
      }

      // localStorage에 저장
      saveScanHistory();
    } else {
      console.error('참가자 혜택 정보가 없습니다.');

      // 스캔 결과 설정 (실패)
      scanResult.value = {
        success: false,
        message: '혜택 정보를 불러오는데 실패했습니다.'
      };

      // 스캔 이력에 추가 (실패)
      const now = new Date();
      scanHistory.value.unshift({
        code: confirmationCode,
        success: false,
        message: '혜택 정보를 불러오는데 실패했습니다.',
        timestamp: now.getTime(),
        dateString: formatDate(now)
      });

      // 이력 최대 10개 유지
      if (scanHistory.value.length > 10) {
        scanHistory.value.pop();
      }

      // localStorage에 저장
      saveScanHistory();
    }
  } catch (error) {
    console.error('참가자 혜택 정보 요청 실패:', error);

    // 오류 메시지 추출
    let errorMessage = '혜택 정보를 불러오는데 실패했습니다.';

    if (error.response && error.response.data) {
      errorMessage = error.response.data.error?.message || errorMessage;
    } else if (error.message) {
      errorMessage = error.message;
    }

    // 스캔 결과 설정 (실패)
    scanResult.value = {
      success: false,
      message: errorMessage
    };

    // 스캔 이력에 추가 (실패)
    const now = new Date();
    scanHistory.value.unshift({
      code: confirmationCode,
      success: false,
      message: errorMessage,
      timestamp: now.getTime(),
      dateString: formatDate(now)
    });

    // 이력 최대 10개 유지
    if (scanHistory.value.length > 10) {
      scanHistory.value.pop();
    }

    // localStorage에 저장
    saveScanHistory();
  }
};

// 스캔 이력 전체 삭제
const clearScanHistory = () => {
  // 확인 메시지 표시
  if (confirm('스캔 이력을 모두 삭제하시겠습니까?')) {
    scanHistory.value = [];
    // localStorage에서 해당 항목만 직접 삭제
    localStorage.removeItem(storageKey.value);
  }
};

// 성공/실패에 따른 클래스 계산
const resultClass = computed(() => {
  if (!scanResult.value) return '';
  return scanResult.value.success ? 'success' : 'error';
});

// 카메라 권한 요청
const requestCameraPermission = async () => {
  try {
    // 카메라 지원 여부 확인
    if (!isCameraSupported.value) {
      throw new Error('이 브라우저는 카메라 접근을 지원하지 않습니다. HTTPS 환경에서 실행 중인지 확인해주세요.');
    }

    await navigator.mediaDevices.getUserMedia({ video: true });
    cameraPermission.value = true;
    initScanner();
  } catch (error) {
    console.error('카메라 접근 권한이 거부되었습니다:', error);
    alert(`카메라 접근 오류: ${error.message || '카메라 접근 권한이 필요합니다.'}`);
  }
};

// QR 코드 스캐너 초기화
const initScanner = () => {
  // qrReader 요소가 존재하는지 확인
  const qrReaderElement = document.getElementById('qr-reader');
  if (!qrReaderElement) {
    return;
  }

  try {
    // 기존 인스턴스가 있는 경우 정리
    if (html5QrCode.value) {
      if (isScanning.value) {
        html5QrCode.value.stop().catch(e => console.error('QR 스캐너 정지 오류:', e));
        isScanning.value = false;
      }
    }

    // 새 인스턴스 생성
    html5QrCode.value = new Html5Qrcode('qr-reader');
  } catch (error) {
    console.error('QR 코드 스캐너 초기화 오류:', error);
    html5QrCode.value = null;
  }
};

// 스캐너 시작/중지 토글
const toggleScanner = async () => {
  if (isScanning.value) {
    // 스캔 중지
    await stopScanner();
  } else {
    // 스캔 시작 전에 이전 스캔 결과 초기화
    scanResult.value = null;

    // 스캔 상태만 변경 (카메라는 이미 실행 중)
    isScanning.value = true;
  }
};

// 스캐너 시작
const startScanner = async () => {
  try {
    // 이미 스캔 중인 경우 처리
    if (isScanning.value) {
      return;
    }

    // 스캐너 초기화 시도
    if (!html5QrCode.value) {
      // DOM이 완전히 렌더링된 후에 스캐너 초기화
      await nextTick();
      initScanner();
      if (!html5QrCode.value) {
        console.error('QR 코드 스캐너 초기화 실패');
        return;
      }
    }

    // 카메라가 이미 실행 중인지 확인 (안전하게 확인)
    try {
      // 카메라 상태를 직접 확인하는 대신, 간단한 방법으로 처리
      // 이미 카메라가 실행 중이면 스캔 상태만 변경
      isScanning.value = true;
      return;
    } catch (e) {
      // 오류 발생 시 계속 진행 (카메라 시작 시도)
    }


    // 카메라 장치 목록 가져오기
    const devices = await Html5Qrcode.getCameras();

    if (devices && devices.length > 0) {
      // 장치 ID 사용 - 이미 선택된 카메라가 있는지 확인
      let cameraId;

      if (currentCameraId.value && devices.some(device => device.id === currentCameraId.value)) {
        // 이미 선택된 카메라가 있는 경우 그것을 사용
        cameraId = currentCameraId.value;
      } else {
        // 처음이거나 이전 카메라가 없는 경우 첫 번째 카메라 사용
        cameraId = devices[0].id;
        currentCameraId.value = cameraId;
      }

      // 화면 크기에 따라 QR 코드 스캔 영역 크기 조정
      const qrBoxSize = Math.min(
        Math.min(window.innerWidth, 500) * 0.7, // 화면 너비 또는 500px 중 작은 값의 70%
        350 // 최대 350px
      );

      const config = {
        fps: 30, // 스캔율 향상을 위해 fps 증가 (10 → 30)
        qrbox: { width: qrBoxSize, height: qrBoxSize },
        aspectRatio: 1.0,
        formatsToSupport: [Html5QrcodeSupportedFormats.QR_CODE], // QR 코드만 스캔하여 성능 향상
        disableFlip: false, // 이미지 뒤집기 활성화 (일부 카메라에서 필요)
        experimentalFeatures: {
          useBarCodeDetectorIfSupported: true // 가능한 경우 기본 BarcodeDetector API 사용
        }
      };

      await html5QrCode.value.start(
        cameraId,
        config,
        onScanSuccess,
        onScanFailure
      );

      isScanning.value = true;
    } else {
      throw new Error('사용 가능한 카메라가 없습니다.');
    }
  } catch (error) {
    console.error('QR 코드 스캐너 시작 실패:', error);

    // 오류 메시지 개선
    let errorMessage = '알 수 없는 오류';

    if (error.message) {
      // 이미 스캔 중인 경우 무시하고 스캔 상태만 활성화
      if (error.message.includes('already scanning')) {
        isScanning.value = true;
        return;
      }
      errorMessage = error.message;
    }

    alert(`QR 코드 스캐너를 시작할 수 없습니다: ${errorMessage}`);
  }
};

// 스캐너 중지 (카메라는 유지하고 QR 인식만 중지)
const stopScanner = async () => {
  if (!html5QrCode.value) return;

  try {
    // 스캔 중인 경우에만 처리
    if (isScanning.value) {
      // 카메라를 완전히 중지하지 않고, QR 인식 콜백만 비활성화
      // 실제로는 스캔이 계속 진행되지만 결과를 무시함
      isScanning.value = false;
    }
  } catch (error) {
    console.error('QR 코드 스캔 비활성화 실패:', error);
  }
};

// 카메라 전환
const switchCamera = async () => {
  // 현재 스캐닝 중이면 먼저 중지
  const wasScanning = isScanning.value;
  if (wasScanning) {
    await stopScanner();
  }

  try {
    // 카메라 장치 목록 가져오기
    const devices = await Html5Qrcode.getCameras();

    if (devices && devices.length > 1) {
      // 현재 사용 중인 카메라의 인덱스 찾기
      const currentIndex = devices.findIndex(device => device.id === currentCameraId.value);
      // 다음 카메라로 전환 (마지막 카메라인 경우 처음으로 돌아감)
      const nextIndex = (currentIndex + 1) % devices.length;
      currentCameraId.value = devices[nextIndex].id;
    } else {
    }

    // 이전 방식으로도 전환 유지 (후용)
    currentCamera.value = currentCamera.value === 'environment' ? 'user' : 'environment';
  } catch (error) {
    console.error('카메라 전환 오류:', error);
  }

  // 이전에 스캐닝 중이었다면 다시 시작
  if (wasScanning) {
    await startScanner();
  }
};

// QR 코드 스캔 성공 처리
const onScanSuccess = async (decodedText) => {
  // 이 함수는 이미 isScanning.value가 true일 때만 호출됨

  // 중복 스캔 방지 (최근 3초 이내 동일 코드 스캔 무시)
  const recentScan = scanHistory.value.find(
    item => item.code === decodedText && (Date.now() - item.timestamp) < 3000
  );

  if (recentScan) {
    return;
  }

  try {
    // 스캔 중지 - 스캔 완료 후 자동으로 다시 스캔되지 않도록
    await stopScanner();

    // 현재 모드에 따라 다른 처리
    if (scanMode.value === 'attendance') {
      // 참석 처리 모드
      await processAttendanceMode(decodedText);
    } else if (scanMode.value === 'redemption') {
      // 혜택 처리 모드
      await fetchAttendeeRedemptionDetails(decodedText);
    } else {
      console.error('알 수 없는 스캔 모드:', scanMode.value);
    }
  } catch (error) {
    console.error('QR 코드 스캔 처리 실패:', error);

    // 스캔 중지 - 오류 발생 시에도 스캔 중지
    await stopScanner();

    // 서버 오류 메시지 추출
    let errorMessage = '처리에 실패했습니다.';

    // 서버 응답에서 오류 메시지 추출 시도
    if (error.response && error.response.data) {
      errorMessage = error.response.data.error?.message || errorMessage;
    } else if (error.message) {
      errorMessage = error.message;
    }

    // 스캔 결과 설정
    scanResult.value = {
      success: false,
      message: errorMessage
    };

    // 스캔 이력에 추가
    const now = new Date();
    scanHistory.value.unshift({
      code: decodedText,
      success: false,
      message: errorMessage,
      timestamp: now.getTime(), // 현재 시간의 타임스탬프
      dateString: formatDate(now) // 날짜 문자열도 저장 (디버깅용)
    });

    // 이력 최대 10개 유지
    if (scanHistory.value.length > 10) {
      scanHistory.value.pop();
    }

    // localStorage에 저장
    saveScanHistory();
  }
};

// 참석 처리 모드 처리
const processAttendanceMode = async (confirmationCode) => {
  try {
    // 스캔 결과로 참석 처리 API 호출
    const result = await processAttendance(confirmationCode);

    // 서버 응답에서 메시지 추출
    // 성공 시에는 data 필드에서 메시지를 가져오고, 없으면 기본 메시지 사용
    const serverMessage = result.data?.message || '참석 처리가 완료되었습니다.';

    // 스캔 결과 설정
    scanResult.value = {
      success: true,
      message: serverMessage,
      attendeeName: result.data?.attendeeName || '참석자'
    };

    // 스캔 이력에 추가
    const now = new Date();
    scanHistory.value.unshift({
      code: confirmationCode,
      success: true,
      message: serverMessage,
      timestamp: now.getTime(), // 현재 시간의 타임스탬프
      dateString: formatDate(now) // 날짜 문자열도 저장 (디버깅용)
    });

    // 이력 최대 10개 유지
    if (scanHistory.value.length > 10) {
      scanHistory.value.pop();
    }

    // localStorage에 저장
    saveScanHistory();
  } catch (error) {
    console.error('참석 처리 실패:', error);

    // 서버 오류 메시지 추출
    let errorMessage = '참석 처리에 실패했습니다.';

    // 서버 응답에서 오류 메시지 추출 시도
    if (error.response && error.response.data) {
      errorMessage = error.response.data.error?.message || errorMessage;
    } else if (error.message) {
      errorMessage = error.message;
    }

    // 스캔 결과 설정
    scanResult.value = {
      success: false,
      message: errorMessage
    };

    // 스캔 이력에 추가
    const now = new Date();
    scanHistory.value.unshift({
      code: confirmationCode,
      success: false,
      message: errorMessage,
      timestamp: now.getTime(), // 현재 시간의 타임스탬프
      dateString: formatDate(now) // 날짜 문자열도 저장 (디버깅용)
    });

    // 이력 최대 10개 유지
    if (scanHistory.value.length > 10) {
      scanHistory.value.pop();
    }

    // localStorage에 저장
    saveScanHistory();

    // 에러 다시 던지기
    throw error;
  }
};

// QR 코드 스캔 실패 처리
const onScanFailure = (error) => {
  // 스캔 실패는 일반적으로 무시 (연속 스캔 중 QR 코드가 없는 경우)
  console.debug('QR 코드 스캔 실패:', error);
};

// 날짜 포맷팅 (YYYY-MM-DD)
const formatDate = (timestamp) => {
  return format(new Date(timestamp), 'yyyy-MM-dd');
};

// 시간 포맷팅 (HH:mm:ss)
const formatTime = (timestamp) => {
  return format(new Date(timestamp), 'HH:mm:ss');
};

// 날짜 및 시간 포맷팅 (YYYY-MM-DD HH:mm:ss)
const formatDateTime = (dateTimeString) => {
  try {
    // 서버에서 받은 날짜 문자열 파싱
    const date = typeof dateTimeString === 'string'
      ? parseISO(dateTimeString)
      : new Date(dateTimeString);

    return format(date, 'yyyy-MM-dd HH:mm:ss');
  } catch (error) {
    console.error('날짜 포맷팅 오류:', error);
    return dateTimeString || '-'; // 오류 시 원본 문자열 반환
  }
};

// 스캔 이력을 날짜별로 그룹화
const groupedScanHistory = computed(() => {
  // 스캔 이력이 없는 경우 빈 배열 반환
  if (!scanHistory.value || scanHistory.value.length === 0) {
    return [];
  }

  const groups = {};

  // 각 항목을 날짜별로 그룹화
  scanHistory.value.forEach(item => {
    // 타임스탬프가 없는 경우 현재 시간 사용
    const timestamp = item.timestamp || Date.now();

    // 날짜 포맷팅
    const date = formatDate(timestamp);

    // 해당 날짜 그룹이 없으면 생성
    if (!groups[date]) {
      groups[date] = [];
    }

    // 그룹에 항목 추가
    groups[date].push(item);
  });

  // 날짜별로 정렬된 배열로 변환 (최신 날짜가 먼저 오도록)
  return Object.entries(groups)
    .map(([date, items]) => ({ date, items }))
    .sort((a, b) => new Date(b.date) - new Date(a.date));
});

// 컴포넌트 마운트 시 초기화
onMounted(async () => {
  // localStorage에서 스캔 이력 불러오기
  loadScanHistory();

  // 카메라 지원 여부 확인
  if (!isCameraSupported.value) {
    console.error('이 브라우저는 카메라 접근을 지원하지 않습니다.');
    cameraPermission.value = false;
    return;
  }

  // 카메라 권한 확인
  try {
    await navigator.mediaDevices.getUserMedia({ video: true });
    cameraPermission.value = true;

    // DOM이 완전히 렌더링된 후에 스캐너 초기화 및 카메라 시작
    await nextTick();

    // 스캐너 초기화
    initScanner();

    // 카메라 시작 (QR 인식은 비활성화 상태로)
    if (html5QrCode.value) {
      try {
        const devices = await Html5Qrcode.getCameras();

        if (devices && devices.length > 0) {
          const cameraId = devices[0].id;
          currentCameraId.value = cameraId;

          // 화면 크기에 따라 QR 코드 스캔 영역 크기 조정
          const qrBoxSize = Math.min(
            Math.min(window.innerWidth, 500) * 0.7, // 화면 너비 또는 500px 중 작은 값의 70%
            350 // 최대 350px
          );

          const config = {
            fps: 30, // 스캔율 향상을 위해 fps 증가 (10 → 30)
            qrbox: { width: qrBoxSize, height: qrBoxSize },
            aspectRatio: 1.0,
            formatsToSupport: [Html5QrcodeSupportedFormats.QR_CODE], // QR 코드만 스캔하여 성능 향상
            disableFlip: false, // 이미지 뒤집기 활성화 (일부 카메라에서 필요)
            experimentalFeatures: {
              useBarCodeDetectorIfSupported: true // 가능한 경우 기본 BarcodeDetector API 사용
            }
          };

          // 카메라 시작 (QR 인식은 비활성화 상태로)
          await html5QrCode.value.start(
            cameraId,
            config,
            // 스캔 중지 상태에서는 결과를 무시하는 콜백
            (decodedText) => {
              if (isScanning.value) {
                onScanSuccess(decodedText);
              }
            },
            onScanFailure
          );

        }
      } catch (error) {
        console.error('카메라 시작 실패:', error);
      }
    }

  } catch (error) {
    console.error('카메라 접근 권한이 없습니다:', error);
    cameraPermission.value = false;
  }

  // 페이지 새로고침 또는 나가기 전에 카메라 중지를 위한 이벤트 핸들러 설정
  setupBeforeUnloadHandler();
});

// 사용자 ID가 변경될 때 스캔 이력 다시 불러오기
watch(() => userId.value, (newUserId, oldUserId) => {
  loadScanHistory();
});

// 페이지 새로고침 또는 나가기 전에 카메라 중지
const handleBeforeUnload = async () => {
  // 카메라 완전히 중지
  if (html5QrCode.value) {
    try {
      await html5QrCode.value.stop();
    } catch (error) {
      console.error('카메라 완전 중지 실패:', error);
    }
  }

  isScanning.value = false;
};

const setupBeforeUnloadHandler = () => {
  window.addEventListener('beforeunload', handleBeforeUnload);
};

// 컴포넌트 마운트 시 beforeunload 이벤트 핸들러 설정
onMounted(() => {
  // 기존 코드...

  // beforeunload 이벤트 핸들러 설정
  setupBeforeUnloadHandler();
});

// 컴포넌트 언마운트 직전 실행
onBeforeUnmount(async () => {
  // 카메라 완전히 중지
  if (html5QrCode.value) {
    try {
      await html5QrCode.value.stop();
    } catch (error) {
      console.error('카메라 완전 중지 실패:', error);
    }
  }

  isScanning.value = false;

  // beforeunload 이벤트 핸들러 제거
  window.removeEventListener('beforeunload', handleBeforeUnload);
});

// 다른 페이지로 이동하기 전에 카메라 중지
onBeforeRouteLeave(async (_, __, next) => {
  // 카메라 완전히 중지
  if (html5QrCode.value) {
    try {
      await html5QrCode.value.stop();
    } catch (error) {
      console.error('카메라 완전 중지 실패:', error);
    }
  }

  isScanning.value = false;
  next(); // 다음 페이지로 이동 허용
});

// 컴포넌트 언마운트 시 정리
onUnmounted(async () => {
  // 카메라 완전히 중지
  if (html5QrCode.value) {
    try {
      await html5QrCode.value.stop();
    } catch (error) {
      console.error('카메라 완전 중지 실패:', error);
    }
  }

  isScanning.value = false;
});
</script>

<style scoped>
.qr-scan-view {
  padding: 15px;
  max-width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 스캔 모드 선택 영역 */
.scan-mode-selector {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin: 15px 0;
  width: 100%;
}

.mode-btn {
  padding: 12px 20px;
  border: 2px solid #ddd;
  border-radius: 8px;
  background-color: #f8f9fa;
  color: #555;
  font-weight: bold;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  max-width: 200px;
}

.mode-btn:hover {
  background-color: #e9ecef;
  border-color: #ccc;
}

.mode-btn.active {
  background-color: #4a90e2;
  color: white;
  border-color: #3a80d2;
}

.current-mode {
  text-align: center;
  margin-bottom: 15px;
  padding: 8px;
  background-color: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #4a90e2;
}

.current-mode p {
  margin: 0;
  font-size: 1rem;
  color: #333;
}

.scanner-container {
  margin: 15px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.qr-reader-container {
  width: 100%;
  max-width: 500px; /* 최대 너비 500px로 제한 */
  height: auto;
  max-height: 500px; /* 최대 높이 500px로 제한 */
  aspect-ratio: 1 / 1; /* 정사각형 비율 유지 */
  margin-bottom: 15px;
  margin-left: auto;
  margin-right: auto; /* 중앙 정렬 */
  border: 2px solid #ddd;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* HTML5-QRCode 라이브러리가 생성하는 요소에 대한 스타일 */
#qr-reader {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
}

#qr-reader__scan_region {
  min-height: 250px;
  max-height: 500px;
  max-width: 500px;
  margin: 0 auto;
}

#qr-reader__dashboard {
  display: none !important; /* 라이브러리 기본 UI 숨김 */
}

.debug-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 8px;
  font-size: 14px;
  color: #666;
  text-align: left;
  width: 100%;
  box-sizing: border-box;
}

.debug-info p {
  margin: 5px 0;
}

.scanner-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
  width: 100%;
  justify-content: center;
}

.control-btn {
  padding: 14px 20px;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  background-color: #4a90e2;
  font-size: 1rem;
  min-width: 120px;
  flex-grow: 1;
  text-align: center;
  /* 모바일에서 터치 영역 확장 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  /* 버튼 그림자 효과 */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.control-btn:active {
  transform: translateY(2px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.control-btn:hover {
  background-color: #357ab8;
}

.control-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
  box-shadow: none;
}

/* 스캔 시작 버튼 */
.scan-btn {
  background-color: #4CAF50; /* 녹색 */
}

.scan-btn:hover {
  background-color: #45a049;
}

/* 스캔 중지 버튼 */
.stop-btn {
  background-color: #f44336; /* 빨간색 */
}

.stop-btn:hover {
  background-color: #d32f2f;
}

/* 다시 스캔하기 버튼 */
.rescan-btn {
  background-color: #2196F3; /* 파란색 */
  font-size: 1.05em;
}

.rescan-btn:hover {
  background-color: #0b7dda;
}

.scan-result {
  margin: 15px 0;
  padding: 15px;
  border-radius: 8px;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
  text-align: center;
}

.scan-result.success {
  background-color: #dff0d8;
  border-left: 5px solid #5cb85c;
}

.scan-result.error {
  background-color: #f2dede;
  border-left: 5px solid #d9534f;
}

.result-message {
  font-size: 16px;
  margin-bottom: 10px;
}

.attendee-name {
  font-size: 18px;
  font-weight: bold;
}

.scan-history {
  margin-top: 25px;
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 15px;
  background-color: #f9f9f9;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.scan-history ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.scan-history li {
  padding: 12px;
  margin-bottom: 8px;
  background-color: #ffffff;
  border-radius: 8px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.scan-history li.success {
  background-color: #f0f8ff;
  border-left: 3px solid #4caf50;
}

.scan-history li:last-child {
  margin-bottom: 0;
}

.scan-history .time {
  font-size: 0.85rem;
  color: #666;
  min-width: 70px;
}

.scan-history .code {
  flex: 1;
  margin: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: monospace;
  font-size: 0.9rem;
}

.scan-history .status {
  font-weight: bold;
  font-size: 0.9rem;
  padding: 3px 8px;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.scan-history li.success .status {
  color: #4caf50;
  background-color: #e8f5e9;
}

.scan-history li.error .status {
  color: #f44336;
  background-color: #ffebee;
}

.scan-history .message {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.9rem;
  margin-top: 5px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.history-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.clear-btn {
  padding: 8px 12px;
  background-color: #f8d7da;
  color: #721c24;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  /* 모바일에서 터치 영역 확장 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.clear-btn:hover {
  background-color: #f1b0b7;
}

.clear-btn:active {
  transform: translateY(1px);
}

.delete-btn {
  padding: 6px 10px;
  background-color: #f8f9fa;
  color: #dc3545;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  /* 모바일에서 터치 영역 확장 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.delete-btn:hover {
  background-color: #f1f1f1;
  color: #c82333;
}

.delete-btn:active {
  transform: translateY(1px);
}

.camera-permission {
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 12px;
  margin: 15px 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.permission-btn {
  padding: 12px 20px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 15px;
  font-size: 1rem;
  font-weight: bold;
  /* 모바일에서 터치 영역 확장 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.permission-btn:active {
  transform: translateY(2px);
}

.permission-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  box-shadow: none;
}

.browser-warning {
  color: #d9534f;
  background-color: #f2dede;
  padding: 12px;
  border-radius: 8px;
  margin: 15px 0;
  font-size: 0.9rem;
  line-height: 1.5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 날짜별 그룹 스타일 */
.history-groups {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.history-group {
  border: 1px solid #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.date-header {
  background-color: #f8f9fa;
  padding: 10px 15px;
  font-weight: bold;
  color: #495057;
  border-bottom: 1px solid #e9ecef;
  font-size: 1rem;
}

.history-items {
  margin: 0;
  padding: 0;
}

/* 모바일 최적화 미디어 쿼리 */
@media (max-width: 768px) {
  .qr-scan-view {
    padding: 10px;
  }

  h1 {
    font-size: 1.3rem;
    margin-bottom: 10px;
  }

  .qr-reader-container {
    margin-bottom: 10px;
  }

  .control-btn {
    padding: 12px 16px;
    font-size: 0.95rem;
    min-width: 110px;
  }

  .scanner-controls {
    justify-content: space-between;
  }

  .scan-history li {
    padding: 10px;
    flex-direction: column;
    align-items: flex-start;
  }

  .scan-history .time,
  .scan-history .status,
  .scan-history .code,
  .scan-history .message {
    margin: 3px 0;
    width: 100%;
  }

  .scan-history .code {
    font-size: 0.85rem;
  }

  .delete-btn {
    margin-top: 5px;
    align-self: flex-end;
  }
}

/* 작은 모바일 화면 최적화 */
@media (max-width: 480px) {
  .control-btn {
    padding: 10px 14px;
    font-size: 0.9rem;
    min-width: 100px;
  }

  .history-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .clear-btn {
    width: 100%;
    text-align: center;
    margin-top: 5px;
  }

  .date-header {
    font-size: 0.9rem;
    padding: 8px 12px;
  }

  .redemption-modal {
    width: 95%;
    max-width: 95%;
  }

  .scan-mode-selector {
    flex-direction: column;
    align-items: center;
  }

  .mode-btn {
    max-width: 100%;
    width: 100%;
  }
}

/* 참가자 혜택 정보 모달 스타일 */
.redemption-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.redemption-modal {
  background-color: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.redemption-modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.redemption-modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  color: #333;
}

.close-btn {
  background-color: #f1f1f1;
  border: 1px solid #ddd;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0 10px;
  margin: 0;
  line-height: 1.5;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  color: #333;
  background-color: #e0e0e0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.redemption-modal-content {
  padding: 20px;
  flex-grow: 1;
  overflow-y: auto;
}

.redemption-modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  background-color: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

.attendee-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #4a90e2;
}

.info-row {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: bold;
  min-width: 120px;
  color: #555;
}

.info-value {
  flex: 1;
  color: #333;
}

.benefits-list {
  margin-top: 20px;
}

.benefits-list h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.1rem;
}

.benefits-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}

.benefits-table th,
.benefits-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.benefits-table th {
  background-color: #f8f9fa;
  font-weight: bold;
  color: #555;
}

.benefits-table tr:last-child td {
  border-bottom: none;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: bold;
}

.status-redeemed {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-not-redeemed {
  background-color: #fff8e1;
  color: #ff9800;
}

.no-benefits,
.loading-benefits {
  padding: 20px;
  text-align: center;
  color: #666;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-top: 15px;
}

/* 혜택 사용 처리 버튼 */
.redeem-btn {
  padding: 6px 12px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: bold;
  transition: all 0.3s ease;
  min-width: 90px;
}

.redeem-btn:hover:not(:disabled) {
  background-color: #45a049;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.redeem-btn:active:not(:disabled) {
  transform: translateY(1px);
}

.redeem-btn.disabled,
.redeem-btn:disabled {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
  opacity: 0.7;
}

/* 모달 닫기 버튼 */
.close-modal-btn {
  background-color: #6c757d;
  min-width: 120px;
  font-size: 1rem;
}

.close-modal-btn:hover {
  background-color: #5a6268;
}

/* 액션 버튼 컨테이너 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

/* 사용 완료 액션 */
.redeemed-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.redeemed-label {
  font-size: 0.9rem;
  color: #4CAF50;
  font-weight: bold;
}

/* 취소 버튼 */
.cancel-btn {
  padding: 4px 8px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: bold;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background-color: #d32f2f;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.cancel-btn:active {
  transform: translateY(1px);
}
</style>
