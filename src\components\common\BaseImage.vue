<template>
  <img :src="currentSrc" :alt="alt" @error="handleImageError" />
</template>

<script setup>
import { ref, watch } from 'vue';
import noImage from '@/assets/image/no-image.png';

const props = defineProps({
  src: {
    type: String,
    required: true,
  },
  alt: {
    type: String,
    default: 'image',
  },
});

const currentSrc = ref(props.src);

watch(() => props.src, (newSrc) => {
  currentSrc.value = newSrc;
});

const handleImageError = (event) => {
  // --- 추가된 로그 ---
  console.error('[BaseImage] Image loading error for src:', props.src);
  console.log('[BaseImage] Event target src before change:', event.target.src);
  console.log('[BaseImage] noImage path:', noImage);
  // --- ---

  currentSrc.value = noImage;

  // --- 추가된 로그 ---
  console.log('[BaseImage] currentSrc set to noImage. New currentSrc:', currentSrc.value);
  // --- ---
};
</script>

<style scoped>
img {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 기본 object-fit 설정 */
}
</style>
