<template>
  <img
    :src="currentSrc"
    :alt="alt"
    :class="imageClass"
    :style="imageStyle"
    @error="handleImageError"
    @load="handleImageLoad"
  />
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import noImage from '@/assets/image/no-image.png';

const props = defineProps({
  src: {
    type: String,
    required: true,
  },
  alt: {
    type: String,
    default: 'image',
  },
  // 추가 스타일링 옵션들
  objectFit: {
    type: String,
    default: 'contain',
    validator: (value) => ['contain', 'cover', 'fill', 'none', 'scale-down'].includes(value)
  },
  width: {
    type: [String, Number],
    default: '100%'
  },
  height: {
    type: [String, Number],
    default: '100%'
  },
  // CSS 클래스 추가 옵션
  imageClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 추가 스타일 옵션
  customStyle: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['load', 'error']);

const currentSrc = ref(props.src);
const hasError = ref(false);

// src prop이 변경될 때마다 currentSrc 업데이트 및 에러 상태 초기화
watch(() => props.src, (newSrc) => {
  currentSrc.value = newSrc;
  hasError.value = false;
});

// 이미지 스타일 계산
const imageStyle = computed(() => {
  const baseStyle = {
    width: typeof props.width === 'number' ? `${props.width}px` : props.width,
    height: typeof props.height === 'number' ? `${props.height}px` : props.height,
    objectFit: props.objectFit,
  };

  return { ...baseStyle, ...props.customStyle };
});

const handleImageError = (event) => {
  // 이미 no-image를 표시하고 있다면 무한 루프 방지
  if (hasError.value || currentSrc.value === noImage) {
    console.error('[BaseImage] Failed to load fallback image:', noImage);
    return;
  }

  console.error('[BaseImage] Image loading error for src:', props.src);
  hasError.value = true;
  currentSrc.value = noImage;

  // 에러 이벤트 발생
  emit('error', event);
};

const handleImageLoad = (event) => {
  hasError.value = false;
  emit('load', event);
};
</script>

<style scoped>
img {
  display: block;
  max-width: 100%;
  height: auto;
}
</style>
