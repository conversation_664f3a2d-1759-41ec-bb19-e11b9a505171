# useImageErrorHandler 컴포저블 사용법

백엔드 서버에서 이미지 로딩 실패 시 자동으로 `no-image.png`로 대체하는 공통 기능입니다.

## 기본 사용법

### 1. Vue 컴포넌트에서 사용

```vue
<template>
  <div>
    <img 
      :src="imageHandler.displaySrc.value" 
      alt="이미지" 
      @error="imageHandler.handleImageError"
      @load="imageHandler.handleImageLoad"
    />
    
    <!-- 에러 상태 표시 (선택사항) -->
    <div v-if="imageHandler.hasError.value" class="error-message">
      이미지를 불러올 수 없습니다
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useImageErrorHandler } from '@/composables/useImageErrorHandler';

const imageSrc = ref('https://example.com/image.jpg');
const imageHandler = useImageErrorHandler(imageSrc);
</script>
```

### 2. 간단한 사용법 (useSimpleImageError)

```vue
<template>
  <img v-bind="imageProps" alt="이미지" />
</template>

<script setup>
import { useSimpleImageError } from '@/composables/useImageErrorHandler';

const imageProps = useSimpleImageError('https://example.com/image.jpg');
</script>
```

### 3. 기존 img 엘리먼트에 적용 (applyImageErrorHandler)

```javascript
import { applyImageErrorHandler } from '@/composables/useImageErrorHandler';

// DOM 엘리먼트에 직접 적용
const imgElement = document.querySelector('#my-image');
const cleanup = applyImageErrorHandler(imgElement, 'https://example.com/image.jpg');

// 컴포넌트 언마운트 시 클린업
onUnmounted(() => {
  cleanup();
});
```

## 고급 사용법

### 1. 로딩 상태와 함께 사용

```vue
<template>
  <div class="image-container">
    <div v-if="imageHandler.isLoading.value" class="loading-spinner">
      로딩 중...
    </div>
    
    <img 
      :src="imageHandler.displaySrc.value" 
      alt="이미지"
      @error="imageHandler.handleImageError"
      @load="imageHandler.handleImageLoad"
      @loadstart="imageHandler.handleImageLoadStart"
      :class="{ 'loading': imageHandler.isLoading.value }"
    />
    
    <div v-if="imageHandler.isNoImage.value" class="no-image-notice">
      기본 이미지가 표시되고 있습니다
    </div>
  </div>
</template>
```

### 2. imageProps 헬퍼 사용

```vue
<template>
  <img v-bind="imageHandler.imageProps.value" alt="이미지" />
</template>

<script setup>
import { useImageErrorHandler } from '@/composables/useImageErrorHandler';

const imageSrc = ref('https://example.com/image.jpg');
const imageHandler = useImageErrorHandler(imageSrc);
</script>
```

### 3. 에러 상태 수동 초기화

```vue
<template>
  <div>
    <img v-bind="imageHandler.imageProps.value" alt="이미지" />
    <button @click="retryImage">다시 시도</button>
  </div>
</template>

<script setup>
import { useImageErrorHandler } from '@/composables/useImageErrorHandler';

const imageSrc = ref('https://example.com/image.jpg');
const imageHandler = useImageErrorHandler(imageSrc);

const retryImage = () => {
  imageHandler.resetErrorState();
  // 이미지 src를 다시 설정하여 재로딩 시도
  imageSrc.value = imageSrc.value + '?retry=' + Date.now();
};
</script>
```

## 반환값 설명

### useImageErrorHandler 반환값

- `hasError`: 이미지 로딩 에러 상태
- `isLoading`: 이미지 로딩 중 상태
- `isNoImage`: no-image가 표시되고 있는지 여부
- `isValidImage`: 유효한 이미지가 로딩되었는지 여부
- `displaySrc`: 실제 표시될 이미지 소스 (에러 시 no-image)
- `sourceImage`: 원본 이미지 소스
- `handleImageError`: 에러 이벤트 핸들러
- `handleImageLoad`: 로드 이벤트 핸들러
- `handleImageLoadStart`: 로드 시작 이벤트 핸들러
- `resetErrorState`: 에러 상태 초기화 함수
- `imageProps`: img 태그에 바인딩할 속성들
- `noImageSrc`: no-image 파일 경로

## 실제 적용 예시

### CanvasProperties.vue에서의 사용

```vue
<template>
  <div v-if="canvasBackgroundImage" class="image-preview">
    <img 
      :src="backgroundImageHandler.displaySrc.value" 
      alt="배경 이미지" 
      :style="{ objectFit: canvasBackgroundFit }" 
      @error="backgroundImageHandler.handleImageError"
      @load="backgroundImageHandler.handleImageLoad"
    />
    <button class="remove-image-btn" @click="removeBackgroundImage">×</button>
    
    <!-- 에러 오버레이 -->
    <div v-if="backgroundImageHandler.hasError.value" class="image-error-overlay">
      <span class="error-text">이미지를 불러올 수 없습니다</span>
    </div>
  </div>
</template>

<script setup>
import { useImageErrorHandler } from '@/composables/useImageErrorHandler';

const backgroundImageHandler = useImageErrorHandler(canvasBackgroundImage);
</script>
```

## 주의사항

1. **무한 루프 방지**: no-image 파일 자체가 로딩 실패하는 경우를 방지하기 위한 로직이 포함되어 있습니다.
2. **메모리 누수 방지**: `applyImageErrorHandler` 사용 시 반드시 클린업 함수를 호출해야 합니다.
3. **개발 환경 로그**: 개발 환경에서만 상세한 로그가 출력됩니다.
4. **반응성**: 이미지 소스가 변경될 때마다 자동으로 에러 상태가 초기화됩니다.
