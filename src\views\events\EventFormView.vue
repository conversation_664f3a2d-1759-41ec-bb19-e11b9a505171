<template>
  <div class="landing-management">
    <h1>{{ isEditing ? '이벤트 수정' : '이벤트 생성' }}</h1>
    <div class="landing-list-container">
      <template v-if="!currentProjectId">
        <div class="no-data">선택된 프로젝트가 없습니다.</div>
      </template>
      <template v-else>
        <div class="form-container">
          <div class="form-group">
            <label>프로젝트</label>
            <input type="text" :value="currentProjectId" disabled />
          </div>
          <div class="form-group">
            <label>이벤트 이름</label>
            <input v-model="form.eventName" placeholder="이벤트 이름을 입력하세요" />
          </div>
          <div class="form-group">
            <label>설명</label>
            <textarea v-model="form.description" placeholder="설명을 입력하세요"></textarea>
          </div>
          <div class="form-group">
            <label>기간</label>
            <div>
              <input type="datetime-local" v-model="form.startDate" />
              &ndash;
              <input type="datetime-local" v-model="form.endDate" />
            </div>
          </div>
          <div class="form-group">
            <label>장소</label>
            <input v-model="form.location" placeholder="장소를 입력하세요" />
          </div>
          <div class="form-group">
            <label>신청 인원 제한 (미입력시 제한 없음)</label>
            <input type="number" v-model.number="form.participantLimit" placeholder="숫자로 입력" />
          </div>
          <div class="form-group">
            <label>연동할 사전 신청서</label>
            <select v-model="form.preRegistrationFormId">
              <option :value="null">사전 신청서 없음</option>
              <option v-for="formOption in preRegistrationForms" :key="formOption.formId" :value="formOption.formId">
                {{ formOption.formName }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>이벤트 이미지 업로드</label>
            <input type="file" @change="handleEventImageUpload" />
            <div v-if="eventImagePreview" class="event-image-preview">
              <img :src="eventImagePreview" alt="이벤트 이미지 미리보기" style="max-width: 200px; margin-top: 8px;" />
              <div class="file-info">
                <div class="file-name">파일명: {{ getFileName() }}</div>
                <button type="button" @click="removeEventImage" class="remove-btn">제거</button>
              </div>
            </div>
          </div>

          <!-- 혜택 관리 섹션 -->
          <div class="form-section">
            <h3>혜택 관리</h3>
            <div class="benefit-form">
              <div class="form-group">
                <label>혜택 코드 *</label>
                <input v-model="currentBenefit.benefitCode" placeholder="예: WELCOME_KIT" />
              </div>
              <div class="form-group">
                <label>혜택 이름 *</label>
                <input v-model="currentBenefit.benefitName" placeholder="예: 웰컴 키트 증정" />
              </div>
              <div class="form-group">
                <label>설명 (선택사항)</label>
                <textarea v-model="currentBenefit.description" placeholder="혜택에 대한 설명을 입력하세요"></textarea>
              </div>
              <div class="form-group">
                <label>재고 수량 (미입력시 수량 제한없음)</label>
                <input type="number" v-model="currentBenefit.quantity" min="0" placeholder="재고 수량을 입력하세요" />
              </div>
              <div class="benefit-actions">
                <button @click="addBenefit" class="add-btn">{{ isEditingBenefit ? '수정' : '추가' }}</button>
                <button v-if="isEditingBenefit" @click="cancelEditBenefit" class="cancel-btn">취소</button>
              </div>
            </div>

            <!-- 혜택 목록 테이블 -->
            <div v-if="benefits.length > 0" class="benefits-table-container">
              <table class="benefits-table">
                <thead>
                  <tr>
                    <th>혜택 코드</th>
                    <th>혜택 이름</th>
                    <th>설명</th>
                    <th>재고</th>
                    <th>작업</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(benefit, index) in benefits" :key="index">
                    <td>{{ benefit.benefitCode }}</td>
                    <td>{{ benefit.benefitName }}</td>
                    <td>{{ benefit.description || '-' }}</td>
                    <td>{{ benefit.quantity !== null ? benefit.quantity : '제한 없음' }}</td>
                    <td class="action-buttons">
                      <button @click="editBenefit(index)" class="edit-btn">수정</button>
                      <button @click="removeBenefit(index)" class="remove-btn">삭제</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else class="no-benefits">
              등록된 혜택이 없습니다.
            </div>
          </div>
        </div>
        <div class="actions-bar">
          <button @click="saveEvent" class="create-btn">저장</button>
          <button @click="cancel" class="cancel-btn">취소</button>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { reactive, computed, ref, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { createEvent, getEvent, updateEvent, deleteEventBenefit } from '@/api/events';
import { getPreRegistrations } from '@/api/preRegistrations';
import { useRouter, useRoute } from 'vue-router';
import { handleApiError } from '@/utils/errorHandler';

const router = useRouter();
const route = useRoute();
const eventId = route.params.eventId;
const isEditing = computed(() => !!eventId);
const authStore = useAuthStore();
const currentProjectId = computed(() => authStore.currentProject?.projectId);

const form = reactive({
  eventName: '',
  description: '',
  startDate: '',
  endDate: '',
  location: '',
  participantLimit: null,
  preRegistrationFormId: null,
  linkedQrCodeId: null,
  status: 'SCHEDULED',
  removeImage: false // 이미지 삭제 플래그
});

// 사전 신청서 목록
const preRegistrationForms = ref([]);

// 혜택 관리 관련 상태
const benefits = ref([]); // 혜택 목록
const currentBenefit = reactive({
  benefitCode: '',
  benefitName: '',
  description: '',
  quantity: null, // 재고 수량 추가 (기본값 null)
  eventBenefitId: null // 혜택 ID 추가 (수정 시 사용)
});
const isEditingBenefit = ref(false); // 혜택 수정 모드 여부
const editingBenefitIndex = ref(-1); // 현재 수정 중인 혜택 인덱스

// 날짜 값을 'yyyy-MM-dd HH:mm:ss.SSS' 형식으로 포맷팅하는 함수
const formatDateTime = (dateString) => {
  if (!dateString) return null;
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  const milliseconds = String(date.getMilliseconds()).padStart(3, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
};

const parseToInputDate = (dateString) => {
  if (!dateString) return '';
  const [date, time] = dateString.split(' ');
  return `${date}T${time.substring(0,5)}`;
};

const eventImageFile = ref(null);
const eventImagePreview = ref('');
const existingImagePath = ref('');

// 백엔드 서버 기본 URL에서 `/api/way` 제거하여 호스트만 사용
const baseUrl = import.meta.env.VITE_API_BASE_URL.replace(/\/api\/way$/, '');

const handleEventImageUpload = (e) => {
  const file = e.target.files && e.target.files[0];
  if (file) {
    eventImageFile.value = file;
    eventImagePreview.value = URL.createObjectURL(file);
    existingImagePath.value = ''; // 새 파일 선택 시 기존 이미지 경로 초기화
    form.removeImage = false; // 새 이미지 선택 시 삭제 플래그 해제
  } else {
    eventImageFile.value = null;
    eventImagePreview.value = '';
  }
};

const removeEventImage = () => {
  eventImageFile.value = null;
  eventImagePreview.value = '';
  existingImagePath.value = '';
  // 이미지 삭제 플래그 추가 (서버에 전송할 때 사용)
  form.removeImage = true;
};

// 파일 이름 가져오기
const getFileName = () => {
  // 새로 업로드한 파일이 있는 경우
  if (eventImageFile.value) {
    return eventImageFile.value.name;
  }

  // 기존 이미지 경로가 있는 경우
  if (existingImagePath.value) {
    // URL인 경우 마지막 '/' 이후의 문자열을 추출
    if (existingImagePath.value.startsWith('http')) {
      const segments = existingImagePath.value.split('/');
      return segments[segments.length - 1];
    }

    // 상대 경로인 경우
    const segments = existingImagePath.value.split('/');
    return segments[segments.length - 1];
  }

  return '';
};

// 혜택 추가 함수
const addBenefit = () => {
  // 필수 필드 검증
  if (!currentBenefit.benefitCode || !currentBenefit.benefitName) {
    alert('혜택 코드와 혜택 이름은 필수 항목입니다.');
    return;
  }

  // 혜택 코드 중복 검사 (수정 모드가 아닐 때만)
  if (!isEditingBenefit.value && benefits.value.some(b => b.benefitCode === currentBenefit.benefitCode)) {
    alert('이미 동일한 혜택 코드가 존재합니다.');
    return;
  }

  if (isEditingBenefit.value) {
    // 수정 모드: 기존 혜택 업데이트
    if (editingBenefitIndex.value >= 0 && editingBenefitIndex.value < benefits.value.length) {
      benefits.value[editingBenefitIndex.value] = { ...currentBenefit };
    }
    // 수정 모드 종료
    isEditingBenefit.value = false;
    editingBenefitIndex.value = -1;
  } else {
    // 추가 모드: 새 혜택 추가
    benefits.value.push({ ...currentBenefit });
  }

  // 입력 필드 초기화
  resetBenefitForm();
};

// 혜택 수정 함수
const editBenefit = (index) => {
  if (index >= 0 && index < benefits.value.length) {
    // 현재 편집 중인 혜택 설정
    const benefit = benefits.value[index];
    currentBenefit.benefitCode = benefit.benefitCode;
    currentBenefit.benefitName = benefit.benefitName;
    currentBenefit.description = benefit.description || '';
    currentBenefit.quantity = benefit.quantity !== undefined ? benefit.quantity : null;
    currentBenefit.eventBenefitId = benefit.eventBenefitId || null; // eventBenefitId 추가

    // 수정 모드 활성화
    isEditingBenefit.value = true;
    editingBenefitIndex.value = index;
  }
};

// 혜택 삭제 함수
const removeBenefit = async (index) => {
  if (index >= 0 && index < benefits.value.length) {
    const benefit = benefits.value[index];

    if (confirm('바로 삭제처리됩니다. 정말로 이 혜택 항목을 삭제하시겠습니까?')) {
      try {
        // 수정 모드이고 eventBenefitId가 있는 경우 API 호출
        if (isEditing.value && benefit.eventBenefitId) {
          // 로딩 표시 (필요시 추가)

          // API 호출하여 서버에서 혜택 삭제
          await deleteEventBenefit(eventId, benefit.eventBenefitId);

          // 성공 메시지 표시
          alert('혜택이 삭제되었습니다.');
        }

        // 로컬 상태에서 혜택 제거
        benefits.value.splice(index, 1);
      } catch (error) {
        // 오류 처리
        alert(error.message || '혜택 삭제 중 오류가 발생했습니다.');
      }
    }
  }
};

// 혜택 수정 취소 함수
const cancelEditBenefit = () => {
  isEditingBenefit.value = false;
  editingBenefitIndex.value = -1;
  resetBenefitForm();
};

// 혜택 폼 초기화 함수
const resetBenefitForm = () => {
  currentBenefit.benefitCode = '';
  currentBenefit.benefitName = '';
  currentBenefit.description = '';
  currentBenefit.quantity = null;
  currentBenefit.eventBenefitId = null; // eventBenefitId 초기화
};

onMounted(async () => {
  if (currentProjectId.value) {
    try {
      const response = await getPreRegistrations(currentProjectId.value);

      // 페이지네이션 응답 구조 처리
      if (response && response.data && response.data.content) {
        // 페이지네이션 형식의 응답에서 content 배열 추출
        preRegistrationForms.value = response.data.content;
      } else if (response && Array.isArray(response)) {
        // 배열 형식으로 직접 반환된 경우
        preRegistrationForms.value = response;
      } else {
        console.error('사전 신청서 목록 형식이 예상과 다릅니다:', response);
        preRegistrationForms.value = [];
      }
    } catch (err) {
      console.error('사전 신청서 목록 로드 오류:', err);
      alert(err.message || '사전 신청서 목록을 가져오는 중 오류가 발생했습니다.');
    }
  }
  if (isEditing.value) {
    try {
      const data = await getEvent(eventId);
      form.eventName = data.eventName;
      form.description = data.description;
      form.startDate = parseToInputDate(data.startDate);
      form.endDate = parseToInputDate(data.endDate);
      form.location = data.location;
      form.participantLimit = data.participantLimit;
      form.preRegistrationFormId = data.preRegistrationFormId;
      form.linkedQrCodeId = data.linkedQrCodeId;
      form.status = data.status;

      // 기존 이미지 경로가 있는 경우 미리보기 설정
      if (data.eventImagePath) {
        existingImagePath.value = data.eventImagePath;
        // 이미지 경로가 이미 전체 URL인 경우 그대로 사용
        if (data.eventImagePath.startsWith('http')) {
          eventImagePreview.value = data.eventImagePath;
        } else {
          // 상대 경로인 경우 baseUrl과 결합
          eventImagePreview.value = `${baseUrl}${data.eventImagePath}`;
        }
      }

      // 기존 혜택 목록이 있는 경우 로드
      if (data.benefits && Array.isArray(data.benefits)) {
        benefits.value = data.benefits.map(benefit => ({
          benefitCode: benefit.benefitCode,
          benefitName: benefit.benefitName,
          description: benefit.description || '',
          quantity: benefit.quantity !== undefined ? benefit.quantity : null,
          eventBenefitId: benefit.eventBenefitId || null // eventBenefitId 추가
        }));
      }
    } catch (err) {
      alert(err.message || '이벤트 정보를 불러오는 중 오류가 발생했습니다.');
    }
  }
});

const saveEvent = async () => {
  if (!currentProjectId.value) {
    alert('프로젝트가 선택되지 않았습니다.');
    return;
  }
  // 필수 항목 검증
  if (!form.eventName || !form.eventName.trim()) {
    alert('이벤트 이름은 필수 항목입니다.');
    return;
  }

  try {
    const formDataToSend = new FormData();
    formDataToSend.append('projectId', currentProjectId.value);
    formDataToSend.append('eventName', form.eventName);
    formDataToSend.append('description', form.description);
    formDataToSend.append('startDate', form.startDate ? formatDateTime(form.startDate) : '');
    formDataToSend.append('endDate', form.endDate ? formatDateTime(form.endDate) : '');
    formDataToSend.append('location', form.location);
    formDataToSend.append('participantLimit', form.participantLimit != null ? form.participantLimit : '');
    formDataToSend.append('preRegistrationFormId', form.preRegistrationFormId != null ? form.preRegistrationFormId : '');
    formDataToSend.append('linkedQrCodeId', form.linkedQrCodeId != null ? form.linkedQrCodeId : '');
    formDataToSend.append('status', form.status);

    // 이미지 처리
    if (eventImageFile.value) {
      // 새 이미지 파일이 있는 경우
      formDataToSend.append('eventImageFile', eventImageFile.value);
    } else if (form.removeImage) {
      // 이미지 삭제를 요청한 경우
      formDataToSend.append('removeImage', 'true');
    } else if (existingImagePath.value && isEditing.value) {
      // 기존 이미지를 유지하는 경우 (수정 시)
      formDataToSend.append('keepExistingImage', 'true');
    }

    // 혜택 목록 처리
    if (benefits.value.length > 0) {
      // 혜택 목록을 가공하여 benefitId 필드 추가
      const processedBenefits = benefits.value.map(benefit => {
        // 기존 혜택 객체 복사
        const processedBenefit = { ...benefit };

        // eventBenefitId가 있는 경우 benefitId로 추가
        if (processedBenefit.eventBenefitId) {
          processedBenefit.benefitId = processedBenefit.eventBenefitId;
        }

        return processedBenefit;
      });

      // 혜택 목록을 JSON 문자열로 변환하여 FormData에 추가
      const benefitsJson = JSON.stringify(processedBenefits);
      formDataToSend.append('benefitsJson', benefitsJson);
    } else {
      // 혜택이 없는 경우 빈 배열 전송
      formDataToSend.append('benefitsJson', '[]');
    }

    if (isEditing.value) {
      await updateEvent(eventId, formDataToSend);
      alert('이벤트가 수정되었습니다.');
      router.push({ name: 'events-management-list' });
    } else {
      const response = await createEvent(formDataToSend);
      if (response.data && response.data.success) {
        alert(response.data.message || '이벤트가 생성되었습니다.');
        router.push({ name: 'events-management-list' });
      } else {
        alert(response.data.message || '이벤트 생성에 실패했습니다.');
      }
    }
  } catch (err) {
    const msg = handleApiError(err, isEditing.value ? '이벤트 수정에 실패했습니다.' : '이벤트 생성에 실패했습니다.');
    alert(msg);
  }
};

const cancel = () => {
  router.back();
};
</script>

<style scoped>
.landing-management {
  padding: 20px;
}
.landing-list-container {
  margin-top: 20px;
}
.form-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.form-group label {
  font-weight: bold;
}
.form-group input,
.form-group textarea {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
}
.actions-bar {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}
.create-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}
.create-btn:hover {
  background-color: #45a049;
}
.cancel-btn {
  background-color: #e0e0e0;
  color: #333;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
.cancel-btn:hover {
  background-color: #d0d0d0;
}
.no-data {
  padding: 20px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-top: 20px;
}
.event-image-preview {
  margin-top: 8px;
}
.file-info {
  margin-top: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.file-name {
  font-size: 0.9em;
  color: #666;
  margin-right: 10px;
}
.remove-btn {
  background-color: #F44336;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
}
.remove-btn:hover {
  background-color: #d32f2f;
}

/* 혜택 관리 스타일 */
.form-section {
  margin-top: 30px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  background-color: #f9f9f9;
}

.form-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 1.2rem;
}

.benefit-form {
  background-color: #fff;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

.benefit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
}

.add-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.add-btn:hover {
  background-color: #45a049;
}

.edit-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
}

.edit-btn:hover {
  background-color: #0b7dda;
}

.benefits-table-container {
  margin-top: 15px;
  overflow-x: auto;
}

.benefits-table {
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.benefits-table th,
.benefits-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.benefits-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #333;
}

.benefits-table tr:last-child td {
  border-bottom: none;
}

.benefits-table tr:hover {
  background-color: #f9f9f9;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.no-benefits {
  padding: 15px;
  text-align: center;
  color: #666;
  background-color: #fff;
  border-radius: 4px;
  border: 1px dashed #ccc;
}
</style>
