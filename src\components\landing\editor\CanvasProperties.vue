<template>
  <div class="canvas-properties">
    <div class="panel-header">
      <h3>캔버스 속성</h3>
    </div>

    <!-- 캔버스 크기 -->
    <div class="property-section">
      <h4>크기</h4>

      <div class="property-row">
        <label>너비 (375px ~ 500px):</label>
        <div class="input-with-slider">
          <input
            type="number"
            :value="canvasWidth"
            @input="updateCanvasWidth($event.target.value)"
            min="375"
            max="500"
            step="1"
          />
          <input
            type="range"
            :value="canvasWidth"
            @input="updateCanvasWidth($event.target.value)"
            min="375"
            max="500"
            step="1"
          />
        </div>
      </div>

      <div class="property-row">
        <label>높이 (500px ~ 5000px):</label>
        <div class="input-with-slider">
          <input
            type="number"
            :value="canvasHeight"
            @input="updateCanvasHeight($event.target.value)"
            min="500"
            max="5000"
            step="100"
          />
          <input
            type="range"
            :value="canvasHeight"
            @input="updateCanvasHeight($event.target.value)"
            min="500"
            max="5000"
            step="100"
          />
        </div>
      </div>
    </div>

    <!-- 캔버스 배경 -->
    <div class="property-section">
      <h4>배경</h4>

      <div class="property-row">
        <label>배경색:</label>
        <input
          type="color"
          :value="canvasBackgroundColor"
          @input="updateCanvasProperty('backgroundColor', $event.target.value)"
        />
      </div>

      <div class="property-row">
        <label>배경 이미지:</label>
        <div class="image-upload-container">
          <div v-if="canvasBackgroundImage" class="image-preview">
            <img
              :src="backgroundImageHandler.displaySrc.value"
              alt="배경 이미지"
              :style="{ objectFit: canvasBackgroundFit }"
              @error="backgroundImageHandler.handleImageError"
              @load="backgroundImageHandler.handleImageLoad"
            />
            <button class="remove-image-btn" @click="removeBackgroundImage">×</button>
            <!-- 이미지 로딩 에러 시 표시되는 오버레이 -->
            <div v-if="backgroundImageHandler.hasError.value" class="image-error-overlay">
              <span class="error-text">이미지를 불러올 수 없습니다</span>
            </div>
          </div>
          <button v-else class="upload-image-btn" @click="openImageUploader">이미지 선택</button>

          <!-- 배경 이미지 맞춤 방식 선택 UI -->
          <div v-if="canvasBackgroundImage" class="background-fit-options">
            <label>맞춤 방식:</label>
            <select
              :value="canvasBackgroundFit"
              @change="updateCanvasProperty('backgroundFit', $event.target.value)"
            >
              <option value="cover">채우기 (Cover)</option>
              <option value="contain">맞추기 (Contain)</option>
              <option value="fill">넘치기 (Fill)</option>
              <option value="none">원본 크기 (None)</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 배경 이미지 업로드 모달 -->
  <ImageUploadModal
    v-if="showImageUploadModal"
    :show="showImageUploadModal"
    @close="closeImageUploadModal"
    @select-image="setBackgroundImage"
  />
</template>

<script setup>
import { ref, computed } from 'vue';
import { useLandingEditorStore } from '@/stores/landingEditor';
import ImageUploadModal from './ImageUploadModal.vue';
import { useImageErrorHandler } from '@/composables/useImageErrorHandler';

// 스토어 접근
const landingEditorStore = useLandingEditorStore();

// 캔버스 속성
const canvasWidth = computed(() => {
  if (!landingEditorStore.currentLandingPage) return 375;
  return landingEditorStore.currentLandingPage.canvas.width;
});

const canvasHeight = computed(() => {
  if (!landingEditorStore.currentLandingPage) return 667;
  return landingEditorStore.currentLandingPage.canvas.height;
});

const canvasBackgroundColor = computed(() => {
  if (!landingEditorStore.currentLandingPage) return '#ffffff';
  return landingEditorStore.currentLandingPage.canvas.backgroundColor;
});

const canvasBackgroundImage = computed(() => {
  if (!landingEditorStore.currentLandingPage) return null;
  return landingEditorStore.currentLandingPage.canvas.backgroundImage;
});

const canvasBackgroundFit = computed(() => {
  if (!landingEditorStore.currentLandingPage) return 'cover';
  return landingEditorStore.currentLandingPage.canvas.backgroundFit || 'cover';
});

// 배경 이미지 에러 처리
const backgroundImageHandler = useImageErrorHandler(canvasBackgroundImage);

// 표시할 이미지 소스 계산 (에러 시 no-image 표시)
const displayImageSrc = computed(() => {
  if (backgroundImageError.value) {
    return noImageSrc;
  }
  return canvasBackgroundImage.value;
});

// 배경 이미지가 변경될 때마다 에러 상태 초기화
watch(canvasBackgroundImage, (newImageSrc) => {
  if (newImageSrc !== currentImageSrc.value) {
    backgroundImageError.value = false;
    currentImageSrc.value = newImageSrc;
  }
});

// 캔버스 너비 업데이트
const updateCanvasWidth = (width) => {
  const numWidth = parseInt(width);
  if (isNaN(numWidth)) return;

  // 캔버스 크기 조정 (높이는 유지)
  landingEditorStore.resizeCanvas(numWidth, canvasHeight.value);
};

// 캔버스 높이 업데이트
const updateCanvasHeight = (height) => {
  const numHeight = parseInt(height);
  if (isNaN(numHeight)) return;

  // 캔버스 크기 조정 (너비는 유지)
  landingEditorStore.resizeCanvas(canvasWidth.value, numHeight);
};

// 캔버스 속성 업데이트
const updateCanvasProperty = (property, value) => {
  landingEditorStore.updateCanvasProperty(property, value);
};

// 배경 이미지 제거
const removeBackgroundImage = () => {
  landingEditorStore.updateCanvasProperty('backgroundImage', null);
};

// 이미지 업로드 모달 상태
const showImageUploadModal = ref(false);

// 이미지 업로더 열기
const openImageUploader = () => {
  showImageUploadModal.value = true;
};

// 이미지 업로드 모달 닫기
const closeImageUploadModal = () => {
  showImageUploadModal.value = false;
};

// 배경 이미지 설정
const setBackgroundImage = (imageUrl) => {
  landingEditorStore.updateCanvasProperty('backgroundImage', imageUrl);
};
</script>

<style scoped>
.canvas-properties {
  padding: 16px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.property-section {
  margin-bottom: 24px;
}

.property-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.property-row {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
}

.property-row label {
  margin-bottom: 4px;
  font-size: 12px;
  color: #333;
}

.property-row input[type="number"],
.property-row input[type="text"] {
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.property-row input[type="color"] {
  width: 100%;
  height: 32px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 2px;
}

.input-with-slider {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-with-slider input[type="range"] {
  width: 100%;
}

.image-upload-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.upload-image-btn {
  padding: 8px 12px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.upload-image-btn:hover {
  background-color: #e0e0e0;
}

.background-fit-options {
  margin-top: 12px;
}

.background-fit-options label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #333;
}

.background-fit-options select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: #fff;
}

/* 이미지 에러 오버레이 스타일 */
.image-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}

.error-text {
  color: white;
  font-size: 12px;
  text-align: center;
  padding: 8px;
}
</style>
