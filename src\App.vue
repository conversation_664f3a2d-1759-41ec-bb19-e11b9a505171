<script setup>
import { RouterView, useRouter, useRoute } from 'vue-router';
import { onMounted, onUnmounted, watch, ref } from 'vue';
import SideNav from '@/components/layout/SideNav.vue';
import NotificationBell from '@/components/inquiry/NotificationBell.vue';
import { useAuthStore } from '@/stores/auth';

const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();

// 사이드바 상태 관리
const isSidebarOpen = ref(false);
const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

// 화면 크기가 변경될 때 사이드바 상태 관리
const handleResize = () => {
  // 화면 너비가 768px 이상이면 사이드바를 열고, 그렇지 않으면 닫음
  if (window.innerWidth >= 768) {
    isSidebarOpen.value = true;
  } else {
    isSidebarOpen.value = false;
  }
};

// 로그인 페이지에서는 로딩 오버레이를 표시하지 않음
const shouldShowLoading = () => {
  // 로그인 페이지 또는 비밀번호 변경 페이지에서는 로딩 표시 안 함
  return authStore.isLoading && route.name !== 'login' && route.name !== 'force-password-change';
};

// URL 쿼리 파라미터에서 랜딩 페이지 ID 감지 및 처리
const checkLandingPageRedirect = () => {
  // URL에서 landingPageId 쿼리 파라미터 추출
  const landingPageId = route.query.landingPageId;

  if (landingPageId) {
    // 현재 경로가 이미 랜딩 페이지로 설정되어 있는지 확인
    if (route.name !== 'public-landing-page' || route.params.id !== landingPageId) {
      // 랜딩 페이지로 리다이렉션 (교체 모드로 설정하여 무한 루프 방지)
      router.replace({ name: 'public-landing-page', params: { id: landingPageId } });
    }
  }
};

// 컴포넌트 마운트 시 초기화
onMounted(() => {
  // 랜딩 페이지 리다이렉션 검사
  checkLandingPageRedirect();

  // 초기 화면 크기에 따라 사이드바 상태 설정
  handleResize();

  // 화면 크기 변경 이벤트 리스너 등록
  window.addEventListener('resize', handleResize);
});

// 컴포넌트 언마운트 시 이벤트 리스너 제거
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

// 라우트 변경 시 랜딩 페이지 리다이렉션 검사
watch(() => route.query, () => {
  checkLandingPageRedirect();
});

// 모바일에서 라우트 변경 시 사이드바 닫기
watch(() => route.path, () => {
  if (window.innerWidth < 768) {
    isSidebarOpen.value = false;
  }
});
</script>

<template>
  <!-- 랜딩 페이지나 공개 이벤트 페이지일 경우 일반 앱 레이아웃을 사용하지 않고 라우터뷰만 표시 -->
  <RouterView v-if="route.name === 'public-landing-page' || route.name === 'public-event-page'" />

  <!-- 랜딩 페이지가 아닌 경우 일반 앱 레이아웃 사용 -->
  <div v-else class="app-container">
    <!-- 로딩 표시: 로그인 페이지에서는 표시하지 않음 -->
    <div v-if="shouldShowLoading()" class="loading-overlay">
      <div class="loading-indicator">Loading...</div>
    </div>

    <!-- 메인 레이아웃: isLoading과 관계없이 항상 렌더링 -->
    <div class="app-layout" :class="{ 'sidebar-open': isSidebarOpen }">
      <!-- 모바일 메뉴 토글 버튼 -->
      <button
        v-if="authStore.isAuthenticated"
        class="mobile-menu-toggle"
        @click="toggleSidebar"
        aria-label="Toggle menu"
      >
        <span class="menu-icon" :class="{ 'open': isSidebarOpen }">
          <span></span>
          <span></span>
          <span></span>
        </span>
      </button>
      
      <!-- 알림 벨 컴포넌트 -->
      <div v-if="authStore.isAuthenticated" class="notification-container">
        <NotificationBell />
      </div>

      <!-- 사이드바 표시 -->
      <SideNav
        v-if="authStore.isAuthenticated"
        :class="{ 'mobile-open': isSidebarOpen }"
      />

      <!-- 사이드바 오버레이 (모바일에서만 표시) -->
      <div
        v-if="authStore.isAuthenticated && isSidebarOpen"
        class="sidebar-overlay"
        @click="toggleSidebar"
      ></div>

      <div class="main-content">
        <RouterView />
      </div>
    </div>
  </div>
</template>

<style scoped>
.app-container {
  display: flex;
  height: 100vh;
  width: 100%;
  position: relative;
}

.app-layout {
  display: flex;
  width: 100%;
  min-height: 100vh;
  position: relative;
}

.main-content {
  flex-grow: 1;
  padding: 20px;
  /* overflow-y: auto; */
  transition: margin-left 0.3s ease;
}

/* 알림 컴포넌트 컨테이너 */
.notification-container {
  position: fixed;
  top: 10px;
  right: 20px;
  z-index: 1000;
}

/* 모바일 메뉴 토글 버튼 */
.mobile-menu-toggle {
  position: fixed;
  top: 15px;
  left: 15px;
  z-index: 1000;
  background: #fff;
  border: none;
  border-radius: 4px;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
  background-color: #f5f5f5;
}

.menu-icon {
  width: 24px;
  height: 18px;
  position: relative;
  transform: rotate(0deg);
  transition: 0.5s ease-in-out;
}

.menu-icon span {
  display: block;
  position: absolute;
  height: 3px;
  width: 100%;
  background: #333;
  border-radius: 3px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: 0.25s ease-in-out;
}

.menu-icon span:nth-child(1) {
  top: 0px;
}

.menu-icon span:nth-child(2) {
  top: 8px;
}

.menu-icon span:nth-child(3) {
  top: 16px;
}

.menu-icon.open span:nth-child(1) {
  top: 8px;
  transform: rotate(135deg);
}

.menu-icon.open span:nth-child(2) {
  opacity: 0;
  left: -60px;
}

.menu-icon.open span:nth-child(3) {
  top: 8px;
  transform: rotate(-135deg);
}

/* 사이드바 오버레이 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
  display: none;
}

/* 로딩 오버레이 스타일 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-indicator {
  font-size: 1.5rem;
}

/* 랜딩 페이지 레이아웃 스타일 */
.landing-page-layout {
  padding: 0;
  margin: 0;
  width: 100%;
  min-height: 100vh;
}

.landing-page-content {
  padding: 0;
  margin: 0;
  width: 100%;
  min-height: 100vh;
}

/* 모바일 반응형 스타일 */
@media (max-width: 767px) {
  .main-content {
    padding: 15px;
    padding-top: 60px; /* 모바일 메뉴 버튼 공간 확보 */
  }

  /* 모바일에서 사이드바 기본 숨김 */
  :deep(.side-nav) {
    position: fixed;
    left: -250px;
    top: 0;
    height: 100%;
    z-index: 999;
    transition: left 0.3s ease;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  }

  /* 사이드바가 열렸을 때 */
  :deep(.side-nav.mobile-open) {
    left: 0;
  }

  /* 사이드바가 열렸을 때 오버레이 표시 */
  .sidebar-open .sidebar-overlay {
    display: block;
  }
}

/* 데스크톱 스타일 */
@media (min-width: 768px) {
  .mobile-menu-toggle {
    display: none; /* 데스크톱에서는 토글 버튼 숨김 */
  }

  .main-content {
    margin-left: 250px; /* 사이드바 너비만큼 여백 추가 */
  }

  :deep(.side-nav) {
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    z-index: 999;
  }
}
</style>
