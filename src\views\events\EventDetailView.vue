<template>
  <div class="event-detail">
    <h1>이벤트 상세보기</h1>
    <div v-if="isLoading" class="loading">이벤트 정보를 불러오는 중...</div>
    <div v-else-if="error" class="error-message">{{ error }}</div>
    <div v-else>
      <div class="detail-item"><strong>이벤트 이름:</strong> {{ event.eventName }}</div>
      <div class="detail-item"><strong>설명:</strong> {{ event.description }}</div>
      <div class="detail-item"><strong>시작일:</strong> {{ event.startDate }}</div>
      <div class="detail-item"><strong>종료일:</strong> {{ event.endDate }}</div>
      <div class="detail-item"><strong>장소:</strong> {{ event.location }}</div>
      <div class="detail-item"><strong>신청 인원 제한:</strong> {{ event.participantLimit }}</div>
      <div class="detail-item"><strong>연동된 사전 신청서 이름:</strong> {{ event.preRegistrationFormName ? event.preRegistrationFormName : '연동 없음' }}</div>
      <div class="detail-item"><strong>상태:</strong> {{ formatEventType(event.status) }}</div>
      <div v-if="event.eventImagePath" class="detail-item">
        <strong>이벤트 이미지:</strong>
        <div>
          <BaseImage :src="eventImageUrl" alt="이벤트 이미지" style="max-width: 300px; margin-top: 8px;" />
          <div class="file-name">파일명: {{ getFileName(event.eventImagePath) }}</div>
        </div>
      </div>

      <!-- 혜택 목록 섹션 -->
      <div class="detail-section">
        <h2>혜택 목록</h2>
        <div v-if="event.benefits && event.benefits.length > 0" class="benefits-table-container">
          <table class="benefits-table">
            <thead>
              <tr>
                <th>혜택 코드</th>
                <th>혜택 이름</th>
                <th>설명</th>
                <th>재고 수량</th>
                <th>사용된 수량</th>
                <th>상태</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="benefit in event.benefits" :key="benefit.eventBenefitId">
                <td>{{ benefit.benefitCode }}</td>
                <td>{{ benefit.benefitName }}</td>
                <td>{{ benefit.description || '-' }}</td>
                <td>{{ benefit.quantity !== null ? benefit.quantity : '제한 없음' }}</td>
                <td>{{ benefit.redeemedQuantity || 0 }}</td>
                <td>{{ benefit.status || '-' }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else class="no-benefits">
          등록된 혜택이 없습니다.
        </div>
      </div>

      <div class="actions">
        <button @click="editEvent" class="edit-btn">수정</button>
        <button @click="deleteEventHandler" class="delete-btn">삭제</button>
        <button @click="goBack" class="back-btn">목록으로</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getEvent, deleteEvent as deleteEventApi } from '@/api/events';

const route = useRoute();
const router = useRouter();
const eventId = route.params.eventId;

const event = ref({});
const isLoading = ref(true);
const error = ref(null);

// 백엔드 서버 기본 URL에서 `/api/way` 제거하여 호스트만 사용
const baseUrl = import.meta.env.VITE_API_BASE_URL.replace(/\/api\/way$/, '');
// 전체 이미지 URL
const eventImageUrl = computed(() => {
  if (!event.value.eventImagePath) return '';
  // 이미지 경로가 이미 전체 URL인 경우 그대로 사용
  if (event.value.eventImagePath.startsWith('http')) {
    return event.value.eventImagePath;
  }
  // 상대 경로인 경우 baseUrl과 결합
  return `${baseUrl}${event.value.eventImagePath}`;
});

const loadEvent = async () => {
  try {
    const data = await getEvent(eventId);
    event.value = data;
  } catch (err) {
    error.value = err.message || '이벤트 정보 불러오기 중 오류가 발생했습니다.';
  } finally {
    isLoading.value = false;
  }
};

const editEvent = () => router.push({ name: 'event-edit', params: { eventId } });
const deleteEventHandler = async () => {
  if (!confirm('정말 삭제하시겠습니까?')) return;
  try {
    await deleteEventApi(eventId);
    router.push({ name: 'events-management' });
  } catch (err) {
    alert(err.message || '삭제 중 오류가 발생했습니다.');
  }
};
const goBack = () => router.back();

// 이벤트 타입 포맷팅
const formatEventType = (type) => {
  const typeMap = {
    'SCHEDULED': '예정',
    'ONGOING': '진행',
    'FINISHED': '종료',
    'CANCELED' : '취소'
  };
  return typeMap[type] || type;
};

// 파일 경로에서 파일 이름 추출
const getFileName = (path) => {
  if (!path) return '';

  // URL인 경우 마지막 '/' 이후의 문자열을 추출
  if (path.startsWith('http')) {
    const segments = path.split('/');
    return segments[segments.length - 1];
  }

  // 상대 경로인 경우
  const segments = path.split('/');
  return segments[segments.length - 1];
};

onMounted(loadEvent);
</script>

<style scoped>
.detail-item { margin-bottom: 12px; }
.actions { margin-top: 20px; display: flex; gap: 8px; }
.edit-btn { background-color: #FFC107; color: black; padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; }
.delete-btn { background-color: #F44336; color: white; padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; }
.back-btn { background-color: #9E9E9E; color: white; padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; }
.loading { padding: 20px; text-align: center; }
.error-message { padding: 20px; color: #f44336; text-align: center; }
.file-name { margin-top: 5px; font-size: 0.9em; color: #666; }

/* 혜택 목록 스타일 */
.detail-section {
  margin-top: 30px;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  background-color: #f9f9f9;
}

.detail-section h2 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 1.2rem;
}

.benefits-table-container {
  margin-top: 15px;
  overflow-x: auto;
}

.benefits-table {
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.benefits-table th,
.benefits-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.benefits-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #333;
}

.benefits-table tr:last-child td {
  border-bottom: none;
}

.benefits-table tr:hover {
  background-color: #f9f9f9;
}

.no-benefits {
  padding: 15px;
  text-align: center;
  color: #666;
  background-color: #fff;
  border-radius: 4px;
  border: 1px dashed #ccc;
}
</style>
