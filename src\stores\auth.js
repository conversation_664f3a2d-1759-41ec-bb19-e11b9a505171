import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { loginUser as apiLoginUser, logoutUser as apiLogoutUser, fetchUserProfile as apiFetchUserProfile, changeInitialPassword as apiChangeInitialPassword } from '@/api/auth';
import { getAllProjects } from '@/api/project';
import apiClient from '@/api/index'; // apiClient 인스턴스도 필요 (헤더 설정용)

export const useAuthStore = defineStore('auth', () => {
  // State
  const accessToken = ref(null); // Access Token 저장 (메모리)
  const user = ref(null); // 로그인한 사용자 정보 (필요에 따라)
  const loginError = ref(null); // 로그인 시 발생한 에러 메시지
  const isLoading = ref(false); // 로그인 진행 상태
  const loginStatus = ref(null); // 로그인 상태 (FORCE_PASSWORD_CHANGE 등)
  const tempUserEmail = ref(null); // 비밀번호 변경 페이지에서 사용할 임시 이메일 저장
  const selectedProjectIndex = ref(0); // 현재 선택된 프로젝트 인덱스

  // Getters
  const isAuthenticated = computed(() => !!accessToken.value);
  // 비밀번호 강제 변경 상태인지 확인하는 getter
  const isForcePasswordChange = computed(() => loginStatus.value === 'FORCE_PASSWORD_CHANGE');

  // 프로젝트 관련 getter
  const userProjects = computed(() => user.value?.projects || []);

  const currentProject = computed(() => {
    // SUPER_ADMIN이고 전체 프로젝트 모드인 경우
    if (user.value?.roleId === 'SUPER_ADMIN' && selectedProjectIndex.value === -1) {
      // 전체 프로젝트를 나타내는 가상 프로젝트 객체 반환
      // projectId를 명시적으로 null로 설정하여 project_id가 할당되지 않도록 함
      return {
        projectId: null,
        projectName: '전체 프로젝트',
        isAllProjectsMode: true
      };
    }

    // 프로젝트가 없는 경우
    if (!user.value || !user.value.projects || user.value.projects.length === 0) {
      return null;
    }

    // 현재 선택된 인덱스의 프로젝트 반환
    return user.value.projects[selectedProjectIndex.value] || user.value.projects[0];
  });

  // 현재 프로젝트 ID를 기반으로 인덱스 찾기
  function findProjectIndexById(projectId) {
    if (!user.value?.projects || user.value.projects.length === 0) {
      return 0;
    }

    const index = user.value.projects.findIndex(p => p.projectId === projectId);
    return index >= 0 ? index : 0;
  }

  // 프로젝트 선택 함수
  function selectProject(index) {
    // 인덱스가 문자열로 전달된 경우 숫자로 변환
    const projectIndex = typeof index === 'string' ? Number(index) : index;

    // 전체 프로젝트 모드 (-1)인 경우 특별 처리
    if (projectIndex === -1) {
      // SUPER_ADMIN만 전체 프로젝트 모드를 사용할 수 있음
      if (user.value?.roleId !== 'SUPER_ADMIN') {
        console.error('SUPER_ADMIN만 전체 프로젝트 모드를 사용할 수 있습니다.');
        return false;
      }

      selectedProjectIndex.value = -1;

      // 전체 프로젝트 모드 설정
      user.value.project_id = null;
      user.value.project_name = '전체 프로젝트';

      // 현재 프로젝트 정보 업데이트 (가상 프로젝트 객체)
      user.value.currentProject = {
        projectId: null,
        projectName: '전체 프로젝트',
        isAllProjectsMode: true
      };

      return true;
    }

    // 일반 프로젝트 선택 처리
    if (!user.value?.projects || projectIndex < 0 || projectIndex >= user.value.projects.length) {
      console.error('유효하지 않은 프로젝트 인덱스:', projectIndex);
      return false;
    }

    selectedProjectIndex.value = projectIndex;
    const project = user.value.projects[projectIndex];

    // 현재 프로젝트 정보 업데이트
    user.value.project_id = project.projectId;
    user.value.project_name = project.projectName;
    user.value.currentProject = project;

    return true;
  }

  // 다른 필요한 getter 추가 가능 (예: getUserRole 등)

  // Actions
  async function login(userEmail, password) {
    // 로그인 페이지에서는 전역 로딩 상태를 사용하지 않음
    // isLoading.value = true; // 주석 처리
    loginError.value = null;
    try {
      // API 호출 (실제 API 함수 이름 사용)
      const responseData = await apiLoginUser(userEmail, password);

      // 로그인 상태 확인 (FORCE_PASSWORD_CHANGE 등)
      if (responseData.status === 'FORCE_PASSWORD_CHANGE') {
        // 초기 비밀번호 변경이 필요한 경우
        loginStatus.value = 'FORCE_PASSWORD_CHANGE';
        tempUserEmail.value = responseData.userEmail || userEmail;
        return { success: true, status: 'FORCE_PASSWORD_CHANGE', message: responseData.message };
      }

      // 일반 로그인 처리
      loginStatus.value = 'NORMAL';
      // HttpOnly 쿠키 방식에서는 refreshToken을 여기서 저장하지 않음
      accessToken.value = responseData.accessToken;
      // *** 중요: Access Token을 API 클라이언트 헤더에 설정 ***
      setAccessToken(accessToken.value);

      // --- 로그인 성공 후 사용자 프로필 즉시 가져오기 ---
      try {
        const fetchedUserData = await apiFetchUserProfile();
        if (fetchedUserData) {
          user.value = fetchedUserData;

          // 프로젝트 정보가 있으면 현재 프로젝트 인덱스 초기화
          if (user.value.projects && user.value.projects.length > 0) {
            // SUPER_ADMIN은 프로젝트를 자동으로 선택하지 않음
            if (user.value.roleId === 'SUPER_ADMIN') {
              selectedProjectIndex.value = -1; // 전체 프로젝트 모드
              user.value.project_id = null; // project_id를 명시적으로 null로 설정
              user.value.project_name = '전체 프로젝트';

              // 현재 프로젝트 정보도 업데이트
              if (user.value.currentProject) {
                user.value.currentProject.projectId = null; // projectId도 null로 설정
              }

            }
            // 현재 project_id가 있으면 해당 프로젝트의 인덱스 찾기
            else if (user.value.project_id) {
              const index = findProjectIndexById(user.value.project_id);
              selectedProjectIndex.value = index;
            }
            // 일반 관리자는 첫 번째 프로젝트 선택
            else {
              selectedProjectIndex.value = 0;
              const firstProject = user.value.projects[0];
              user.value.project_id = firstProject.projectId;
              user.value.project_name = firstProject.projectName;
            }
          }
        } else {
          // 프로필은 못 가져왔지만 로그인은 성공한 상태일 수 있음
          // 이 경우 user는 null이지만 accessToken은 유효함
          console.warn('Login successful, but failed to fetch user profile immediately.');
          user.value = null; // 명시적으로 null 설정
        }
      } catch (profileError) {
        console.error('Error fetching user profile after login:', profileError);
        // 프로필 로딩 실패해도 로그인 자체는 성공했을 수 있으므로 토큰은 유지
        user.value = null; // 에러 발생 시 user는 null로 설정
        // 로그인 에러 메시지에 반영할 수도 있음 (선택 사항)
        // loginError.value = '로그인은 성공했으나 사용자 정보를 가져오는데 실패했습니다.';
      }
      // --- 사용자 프로필 가져오기 끝 ---


      // 성공 시 에러 초기화 (프로필 로딩 에러가 발생해도 로그인 에러는 null일 수 있음)
      loginError.value = null;

      return { success: true, status: 'NORMAL' }; // 로그인 성공 정보 반환
    } catch (error) {
      console.error('Pinia login action error:', error);

      // 에러 메시지가 비밀번호 변경 관련인지 확인
      if (error.message && error.message.includes('비밀번호를 변경해야 합니다')) {
        // 비밀번호 강제 변경 상태로 처리
        loginStatus.value = 'FORCE_PASSWORD_CHANGE';
        tempUserEmail.value = userEmail;
        return { success: true, status: 'FORCE_PASSWORD_CHANGE', message: error.message, userEmail: userEmail };
      }

      loginError.value = error.message || '로그인 처리 중 오류 발생';
      // 토큰 및 사용자 정보 초기화
      accessToken.value = null;
      user.value = null;
      return { success: false, error: error.message }; // 로그인 실패 정보 반환
    } finally {
      // 로그인 페이지에서는 전역 로딩 상태를 사용하지 않음
      // isLoading.value = false; // 주석 처리
    }
  }

  async function logout() {
    // 서버에 로그아웃 요청 보내기 (Access Token은 인터셉터에서 추가됨)
    try {
      await apiLogoutUser();
    } catch (error) {
      // 로그아웃 API 실패해도 클라이언트 측 로그아웃은 진행
      console.error('Logout API call failed:', error.message);
    } finally {
      // Access Token 및 사용자 정보 로컬에서 삭제
      accessToken.value = null;
      user.value = null;
      loginError.value = null;
      loginStatus.value = null;
      tempUserEmail.value = null;
      // API 클라이언트 헤더에서도 토큰 제거 (중요)
      delete apiClient.defaults.headers.common['Authorization'];
      // 필요시 로그인 페이지로 리다이렉션
      // router.push('/login');
    }
  }

  function setAccessToken(newToken) {
    accessToken.value = newToken;
    // API 클라이언트 기본 헤더에도 설정 (중요)
    if (newToken) {
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;
    } else {
      delete apiClient.defaults.headers.common['Authorization'];
    }
  }

  // 앱 초기 로드 시 인증 상태 확인
  async function checkAuthStatus() {
    // 로그인 페이지에서는 로딩 상태를 표시하지 않음
    // 로딩 타임아웃 사용하지 않고 직접 인증 상태 확인
    loginError.value = null;

    try {
      // 백엔드에 사용자 정보 요청 시도 (api/auth.js에서 응답의 data 필드를 반환하도록 수정됨)
      const fetchedUserData = await apiFetchUserProfile();

      // fetchedUserData가 성공적으로 반환되었는지 확인 (null이나 undefined가 아닌지)
      if (fetchedUserData) {
        // 현재 선택된 프로젝트 인덱스 저장 (페이지 새로고침 시 복원하기 위해)
        const savedProjectIndex = selectedProjectIndex.value;

        // 성공 시 스토어 상태 업데이트
        user.value = fetchedUserData; // API 응답의 data 필드를 user 상태에 저장

        // 중요: API 호출에 사용될 헤더에 Access Token 설정
        // persist 플러그인 등으로 복원된 accessToken을 사용하거나 이전 단계에서 설정된 값 사용
        if (accessToken.value && !apiClient.defaults.headers.common['Authorization']) {
          setAccessToken(accessToken.value);
        }

        // SUPER_ADMIN인 경우 모든 프로젝트 로드
        if (user.value.roleId === 'SUPER_ADMIN') {
          try {
            const success = await loadAllProjects();
            if (success) {
            } else {
              console.warn('SUPER_ADMIN 모든 프로젝트 로드 실패');
            }
          } catch (projectError) {
            console.error('SUPER_ADMIN 모든 프로젝트 로드 중 오류 발생:', projectError);
          }
        }

        // 저장된 프로젝트 인덱스 복원
        if (savedProjectIndex !== undefined && user.value.projects && user.value.projects.length > 0) {

          // 전체 프로젝트 모드 (-1)인 경우
          if (savedProjectIndex === -1) {
            if (user.value.roleId === 'SUPER_ADMIN') {
              selectedProjectIndex.value = -1;
              user.value.project_id = null;
              user.value.project_name = '전체 프로젝트';

              // 현재 프로젝트 정보 업데이트 (가상 프로젝트 객체)
              user.value.currentProject = {
                projectId: null,
                projectName: '전체 프로젝트',
                isAllProjectsMode: true
              };
            } else {
              // SUPER_ADMIN이 아닌 경우 첫 번째 프로젝트 선택
              selectedProjectIndex.value = 0;
              const firstProject = user.value.projects[0];
              user.value.project_id = firstProject.projectId;
              user.value.project_name = firstProject.projectName;
              user.value.currentProject = firstProject;
            }
          }
          // 일반 프로젝트 선택 모드인 경우
          else if (savedProjectIndex >= 0 && savedProjectIndex < user.value.projects.length) {
            selectedProjectIndex.value = savedProjectIndex;
            const project = user.value.projects[savedProjectIndex];
            user.value.project_id = project.projectId;
            user.value.project_name = project.projectName;
            user.value.currentProject = project;
          }
          // 유효하지 않은 인덱스인 경우 첫 번째 프로젝트 선택
          else {
            selectedProjectIndex.value = 0;
            const firstProject = user.value.projects[0];
            user.value.project_id = firstProject.projectId;
            user.value.project_name = firstProject.projectName;
            user.value.currentProject = firstProject;
          }
        }
      } else {
        // API는 성공했지만 필요한 데이터가 없는 경우
        console.warn('User profile fetched but data is null or invalid.');
        // 상태를 확실히 초기화
        accessToken.value = null;
        user.value = null;
        delete apiClient.defaults.headers.common['Authorization'];
      }
    } catch (error) {
      // fetchUserProfile 실패 (예: 유효하지 않은 토큰, 네트워크 에러 등)
      // 실패 시 명시적으로 로그아웃 상태로 만듦
      accessToken.value = null;
      user.value = null;
      delete apiClient.defaults.headers.common['Authorization'];
    }
    // 로딩 상태를 설정하지 않음 (로그인 페이지에서 로딩 표시 방지)
  }

  // 초기 비밀번호 변경 함수
  async function changeInitialPassword(currentPassword, newPassword) {
    isLoading.value = true;
    loginError.value = null;
    try {
      // 임시 저장된 이메일 사용
      const userEmail = tempUserEmail.value;

      if (!userEmail) {
        throw new Error('사용자 이메일 정보가 없습니다. 다시 로그인해주세요.');
      }

      // 초기 비밀번호 변경 API 호출
      const result = await apiChangeInitialPassword(userEmail, currentPassword, newPassword);

      // API에서 반환한 성공 메시지 사용 또는 기본 메시지 사용
      return {
        success: true,
        message: result.message || '비밀번호가 성공적으로 변경되었습니다. 다시 로그인해주세요.'
      };
    } catch (error) {
      console.error('초기 비밀번호 변경 오류:', error);

      // 에러 메시지 처리
      let errorMessage = error.message || '비밀번호 변경 중 오류가 발생했습니다.';

      // 특정 에러 메시지에 대한 사용자 친화적 메시지 추가
      if (errorMessage.includes('현재 비밀번호가 일치하지 않습니다')) {
        errorMessage = '현재 비밀번호가 일치하지 않습니다. 다시 확인해주세요.';
      }

      loginError.value = errorMessage;
      return { success: false, error: errorMessage };
    } finally {
      isLoading.value = false;
    }
  }

  // 비밀번호 변경 상태 초기화 함수
  function resetPasswordChangeState() {
    loginStatus.value = null;
    tempUserEmail.value = null;
  }

  // SUPER_ADMIN이 모든 프로젝트를 로드하는 함수
  async function loadAllProjects() {
    try {

      // SUPER_ADMIN 권한 확인
      if (!user.value || user.value.roleId !== 'SUPER_ADMIN') {
        console.warn('SUPER_ADMIN 권한이 없어 모든 프로젝트를 로드할 수 없습니다.');
        return false;
      }

      // 모든 프로젝트 로드
      const response = await getAllProjects();

      if (response && response.success && response.data && response.data.content) {
        // 프로젝트 목록 저장
        const allProjects = response.data.content;

        // 사용자 정보에 프로젝트 목록 추가
        if (!user.value.projects) {
          user.value.projects = [];
        }

        // 기존 프로젝트 목록을 모든 프로젝트로 대체
        user.value.projects = allProjects;

        // SUPER_ADMIN은 프로젝트를 자동으로 선택하지 않음
        if (user.value.roleId === 'SUPER_ADMIN') {
          selectedProjectIndex.value = -1; // 전체 프로젝트 모드
          user.value.project_id = null; // project_id를 명시적으로 null로 설정
          user.value.project_name = '전체 프로젝트';

          // 현재 프로젝트 정보도 업데이트
          if (user.value.currentProject) {
            user.value.currentProject.projectId = null; // projectId도 null로 설정
          }

        }
        // 일반 관리자는 첫 번째 프로젝트 선택
        else if (allProjects.length > 0) {
          selectedProjectIndex.value = 0;
          const firstProject = allProjects[0];
          user.value.project_id = firstProject.projectId;
          user.value.project_name = firstProject.projectName;
        }

        return true;
      } else {
        console.error('모든 프로젝트 로드 실패: 응답 구조가 예상과 다릅니다.', response);
        return false;
      }
    } catch (error) {
      console.error('모든 프로젝트 로드 중 오류 발생:', error);
      return false;
    }
  }

  return {
    // State
    accessToken,
    user,
    loginError,
    isLoading,
    loginStatus,
    tempUserEmail, // 임시 이메일 저장용 변수 (ref로 노출하여 직접 수정 가능)
    selectedProjectIndex, // 현재 선택된 프로젝트 인덱스
    // Getters
    isAuthenticated,
    isForcePasswordChange,
    userProjects,
    currentProject,
    // Actions
    findProjectIndexById,
    selectProject,
    // 기존 Actions
    login,
    logout,
    setAccessToken,
    checkAuthStatus,
    changeInitialPassword,
    resetPasswordChangeState, // 비밀번호 변경 상태 초기화 함수
    loadAllProjects, // SUPER_ADMIN이 모든 프로젝트를 로드하는 함수
  };
}, {
  persist: true, // 이 부분을 추가합니다.
});
