<template>
  <div class="image-error-handler-example">
    <h2>이미지 에러 처리 예시</h2>
    
    <!-- 예시 1: 기본 사용법 -->
    <div class="example-section">
      <h3>1. 기본 사용법</h3>
      <div class="image-container">
        <img 
          :src="basicImageHandler.displaySrc.value" 
          alt="기본 예시 이미지" 
          @error="basicImageHandler.handleImageError"
          @load="basicImageHandler.handleImageLoad"
          class="example-image"
        />
        <div class="image-info">
          <p>상태: {{ basicImageHandler.hasError.value ? '에러' : '정상' }}</p>
          <p>로딩 중: {{ basicImageHandler.isLoading.value ? '예' : '아니오' }}</p>
          <p>no-image 표시: {{ basicImageHandler.isNoImage.value ? '예' : '아니오' }}</p>
        </div>
      </div>
      <div class="controls">
        <button @click="setValidImage">유효한 이미지 로드</button>
        <button @click="setInvalidImage">잘못된 이미지 로드</button>
        <button @click="basicImageHandler.resetErrorState">에러 상태 초기화</button>
      </div>
    </div>

    <!-- 예시 2: imageProps 헬퍼 사용 -->
    <div class="example-section">
      <h3>2. imageProps 헬퍼 사용</h3>
      <div class="image-container">
        <img 
          v-bind="helperImageHandler.imageProps.value" 
          alt="헬퍼 예시 이미지"
          class="example-image"
        />
        <div class="image-info">
          <p>상태: {{ helperImageHandler.hasError.value ? '에러' : '정상' }}</p>
        </div>
      </div>
      <div class="controls">
        <input 
          v-model="helperImageSrc" 
          placeholder="이미지 URL 입력"
          class="url-input"
        />
        <button @click="updateHelperImage">이미지 변경</button>
      </div>
    </div>

    <!-- 예시 3: 간단한 사용법 -->
    <div class="example-section">
      <h3>3. 간단한 사용법 (useSimpleImageError)</h3>
      <div class="image-container">
        <img 
          v-bind="simpleImageProps" 
          alt="간단한 예시 이미지"
          class="example-image"
        />
        <div class="image-info">
          <p>에러 상태: {{ simpleImageProps.hasError.value ? '예' : '아니오' }}</p>
        </div>
      </div>
    </div>

    <!-- 예시 4: 로딩 상태와 함께 -->
    <div class="example-section">
      <h3>4. 로딩 상태 표시</h3>
      <div class="image-container">
        <div v-if="loadingImageHandler.isLoading.value" class="loading-overlay">
          <div class="loading-spinner"></div>
          <p>이미지 로딩 중...</p>
        </div>
        
        <img 
          :src="loadingImageHandler.displaySrc.value" 
          alt="로딩 예시 이미지"
          @error="loadingImageHandler.handleImageError"
          @load="loadingImageHandler.handleImageLoad"
          @loadstart="loadingImageHandler.handleImageLoadStart"
          class="example-image"
          :class="{ 'loading': loadingImageHandler.isLoading.value }"
        />
        
        <div v-if="loadingImageHandler.isNoImage.value" class="no-image-notice">
          기본 이미지가 표시되고 있습니다
        </div>
      </div>
      <div class="controls">
        <button @click="loadSlowImage">느린 이미지 로드</button>
        <button @click="loadFailImage">실패하는 이미지 로드</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useImageErrorHandler, useSimpleImageError } from '@/composables/useImageErrorHandler';

// 예시 1: 기본 사용법
const basicImageSrc = ref('https://picsum.photos/200/150?random=1');
const basicImageHandler = useImageErrorHandler(basicImageSrc);

// 예시 2: imageProps 헬퍼 사용
const helperImageSrc = ref('https://picsum.photos/200/150?random=2');
const helperImageHandler = useImageErrorHandler(helperImageSrc);

// 예시 3: 간단한 사용법
const simpleImageProps = useSimpleImageError('https://picsum.photos/200/150?random=3');

// 예시 4: 로딩 상태
const loadingImageSrc = ref('https://picsum.photos/200/150?random=4');
const loadingImageHandler = useImageErrorHandler(loadingImageSrc);

// 컨트롤 함수들
const setValidImage = () => {
  basicImageSrc.value = `https://picsum.photos/200/150?random=${Date.now()}`;
};

const setInvalidImage = () => {
  basicImageSrc.value = 'https://invalid-url-that-will-fail.com/image.jpg';
};

const updateHelperImage = () => {
  helperImageHandler.sourceImage.value = helperImageSrc.value;
};

const loadSlowImage = () => {
  // 느린 이미지 로딩 시뮬레이션
  loadingImageSrc.value = `https://picsum.photos/800/600?random=${Date.now()}`;
};

const loadFailImage = () => {
  loadingImageSrc.value = 'https://this-will-definitely-fail.com/image.jpg';
};
</script>

<style scoped>
.image-error-handler-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.example-section h3 {
  margin-top: 0;
  color: #333;
}

.image-container {
  position: relative;
  display: flex;
  gap: 20px;
  align-items: flex-start;
  margin-bottom: 15px;
}

.example-image {
  width: 200px;
  height: 150px;
  object-fit: cover;
  border: 1px solid #ccc;
  border-radius: 4px;
  transition: opacity 0.3s ease;
}

.example-image.loading {
  opacity: 0.7;
}

.image-info {
  flex: 1;
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #eee;
}

.image-info p {
  margin: 5px 0;
  font-size: 14px;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.controls button {
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.controls button:hover {
  background-color: #0056b3;
}

.url-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 300px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  z-index: 10;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-image-notice {
  position: absolute;
  bottom: 5px;
  left: 5px;
  right: 5px;
  background-color: rgba(255, 193, 7, 0.9);
  color: #333;
  padding: 5px;
  border-radius: 3px;
  font-size: 12px;
  text-align: center;
}
</style>
