import { ref, computed, watch } from 'vue';
import noImageSrc from '@/assets/image/no-image.png';

/**
 * 이미지 에러 처리를 위한 공통 컴포저블
 * 백엔드 서버에서 이미지 로딩 실패 시 자동으로 no-image.png로 대체
 * 
 * @param {Ref|String} imageSrc - 원본 이미지 소스 (reactive 또는 일반 문자열)
 * @returns {Object} 이미지 에러 처리 관련 상태와 메서드들
 */
export function useImageErrorHandler(imageSrc) {
  // 에러 상태 관리
  const hasError = ref(false);
  const isLoading = ref(false);
  const currentSrc = ref(null);

  // 원본 이미지 소스를 reactive하게 처리
  const sourceImage = computed(() => {
    return typeof imageSrc === 'object' && imageSrc.value !== undefined 
      ? imageSrc.value 
      : imageSrc;
  });

  // 실제 표시될 이미지 소스 (에러 시 no-image로 대체)
  const displaySrc = computed(() => {
    if (hasError.value) {
      return noImageSrc;
    }
    return sourceImage.value;
  });

  // 이미지 소스가 변경될 때마다 에러 상태 초기화
  watch(sourceImage, (newSrc, oldSrc) => {
    if (newSrc !== oldSrc && newSrc !== currentSrc.value) {
      resetErrorState();
      currentSrc.value = newSrc;
    }
  }, { immediate: true });

  // 에러 상태 초기화
  const resetErrorState = () => {
    hasError.value = false;
    isLoading.value = false;
  };

  // 이미지 로딩 시작 핸들러
  const handleImageLoadStart = () => {
    isLoading.value = true;
    hasError.value = false;
  };

  // 이미지 로딩 성공 핸들러
  const handleImageLoad = (event) => {
    isLoading.value = false;
    hasError.value = false;
    
    // 로딩 성공 이벤트 로그 (개발 환경에서만)
    if (import.meta.env.DEV) {
      console.log('[ImageErrorHandler] Image loaded successfully:', sourceImage.value);
    }
    
    return event;
  };

  // 이미지 로딩 에러 핸들러
  const handleImageError = (event) => {
    // 이미 no-image를 표시하고 있다면 무한 루프 방지
    if (hasError.value || currentSrc.value === noImageSrc) {
      console.error('[ImageErrorHandler] Failed to load fallback image:', noImageSrc);
      return event;
    }

    isLoading.value = false;
    hasError.value = true;
    
    // 에러 로그
    console.error('[ImageErrorHandler] Image loading failed for:', sourceImage.value);
    console.log('[ImageErrorHandler] Switching to fallback image:', noImageSrc);
    
    return event;
  };

  // 이미지 속성을 위한 헬퍼 객체
  const imageProps = computed(() => ({
    src: displaySrc.value,
    onError: handleImageError,
    onLoad: handleImageLoad,
    onLoadstart: handleImageLoadStart
  }));

  // 이미지 상태 확인 유틸리티
  const isNoImage = computed(() => hasError.value);
  const isValidImage = computed(() => !hasError.value && sourceImage.value);

  return {
    // 상태
    hasError,
    isLoading,
    isNoImage,
    isValidImage,
    
    // 이미지 소스
    displaySrc,
    sourceImage,
    
    // 이벤트 핸들러
    handleImageError,
    handleImageLoad,
    handleImageLoadStart,
    
    // 유틸리티
    resetErrorState,
    imageProps,
    
    // 상수
    noImageSrc
  };
}

/**
 * 단순한 이미지 에러 처리를 위한 헬퍼 함수
 * 기본적인 에러 처리만 필요한 경우 사용
 * 
 * @param {String} imageSrc - 이미지 소스
 * @returns {Object} 간단한 이미지 에러 처리 객체
 */
export function useSimpleImageError(imageSrc) {
  const { displaySrc, handleImageError, handleImageLoad, hasError } = useImageErrorHandler(ref(imageSrc));
  
  return {
    src: displaySrc,
    onError: handleImageError,
    onLoad: handleImageLoad,
    hasError
  };
}

/**
 * Vue 템플릿에서 직접 사용할 수 있는 이미지 에러 처리 디렉티브 스타일 함수
 * 
 * @param {HTMLImageElement} imgElement - img 엘리먼트
 * @param {String} originalSrc - 원본 이미지 소스
 */
export function applyImageErrorHandler(imgElement, originalSrc) {
  if (!imgElement || imgElement.tagName !== 'IMG') {
    console.warn('[ImageErrorHandler] Invalid image element provided');
    return;
  }

  let hasAppliedFallback = false;

  const errorHandler = (event) => {
    if (hasAppliedFallback) {
      console.error('[ImageErrorHandler] Fallback image also failed to load');
      return;
    }

    console.error('[ImageErrorHandler] Image loading failed:', originalSrc);
    hasAppliedFallback = true;
    imgElement.src = noImageSrc;
  };

  const loadHandler = () => {
    hasAppliedFallback = false;
  };

  // 이벤트 리스너 등록
  imgElement.addEventListener('error', errorHandler);
  imgElement.addEventListener('load', loadHandler);

  // 초기 src 설정
  imgElement.src = originalSrc;

  // 클린업 함수 반환
  return () => {
    imgElement.removeEventListener('error', errorHandler);
    imgElement.removeEventListener('load', loadHandler);
  };
}
