<template>
  <div class="public-landing-page">
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>랜딩 페이지를 불러오는 중...</p>
    </div>

    <div v-else-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <h2>오류가 발생했습니다</h2>
      <p>{{ error }}</p>
    </div>

    <div v-else-if="landingPage" class="landing-content">
      <LandingPreview
        :landing-page="landingPage"
        :scale="1"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import publicApiClient from '@/api/publicApi';
import LandingPreview from '@/components/landing/preview/LandingPreview.vue';

const route = useRoute();
const landingPage = ref(null);
const isLoading = ref(true);
const error = ref(null);

// 랜딩 페이지 데이터 로드
const loadLandingPage = async (landingPageId) => {
  isLoading.value = true;
  error.value = null;

  try {
    // 백엔드 API 호출 (인증 없이 공개 API 사용)
    // 공개 접근을 위해 /public 경로 사용
    const response = await publicApiClient.get(`/public/landing/${landingPageId}`);

    // API 응답 확인
    if (response.data && response.data.success) {
      const landingPageData = response.data.data;

      // contentJson 파싱
      if (landingPageData.contentJson) {
        try {
          // contentJson이 문자열인 경우 파싱
          const contentJsonData = typeof landingPageData.contentJson === 'string'
            ? JSON.parse(landingPageData.contentJson)
            : landingPageData.contentJson;


          // 랜딩 페이지 데이터 구성
          landingPage.value = {
            landingPageId: landingPageData.landingPageId,
            pageTitle: landingPageData.pageTitle,
            description: landingPageData.description,
            status: landingPageData.status,
            // contentJson에서 캔버스와 요소 추출
            canvas: contentJsonData.canvas || {},
            elements: contentJsonData.elements || []
          };

        } catch (parseError) {
          console.error('contentJson 파싱 오류:', parseError);
          error.value = 'contentJson 데이터 파싱에 실패했습니다.';
        }
      } else {
        error.value = '랜딩 페이지 콘텐츠 데이터가 없습니다.';
      }
    } else {
      error.value = response.data?.error?.message || '랜딩 페이지를 불러오는 데 실패했습니다.';
    }
  } catch (err) {
    console.error('랜딩 페이지 로드 오류:', err);
    if (err.response && err.response.status === 404) {
      // 404 에러: 서버 메시지 노출
      const serverMessage = err.response.data?.error?.message || err.response.data?.message || err.response.statusText;
      error.value = serverMessage;
    } else {
      error.value = err.message || '랜딩 페이지 데이터를 불러오는 중 오류가 발생했습니다.';
    }
  } finally {
    isLoading.value = false;
  }
};

// body에 클래스 추가하여 랜딩 페이지임을 표시
const addLandingPageBodyClass = () => {
  document.body.classList.add('landing-page-body');
};

// 컴포넌트 언마운트 시 body 클래스 제거
const removeLandingPageBodyClass = () => {
  document.body.classList.remove('landing-page-body');
};

onMounted(() => {
  // body에 클래스 추가
  addLandingPageBodyClass();

  // URL에서 랜딩 페이지 ID 추출
  const landingPageId = route.params.id;
  if (landingPageId) {
    loadLandingPage(landingPageId);
  } else {
    error.value = '랜딩 페이지 ID가 유효하지 않습니다.';
    isLoading.value = false;
  }
});

// 컴포넌트 언마운트 시 클래스 제거
onUnmounted(() => {
  removeLandingPageBodyClass();
});
</script>

<style>
/* 전역 스타일 - 기본 스타일 초기화 */
:root {
  --landing-max-width: 500px;
}

body.landing-page-body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  overflow-x: hidden;
}

/* 랜딩 페이지에서는 기본 앱 스타일 초기화 */
body.landing-page-body #app {
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
  max-width: none;
  max-height: none;
  overflow: visible;
}
</style>

<style scoped>
.public-landing-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background-color: #f5f5f5;
  padding: 0;
  margin: 0;
  overflow-x: hidden;
}

.landing-content {
  width: 100%;
  max-width: var(--landing-max-width); /* 모바일 화면 최대 너비 */
  margin: 0 auto;
  min-height: 100vh;
  overflow: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh;
  background-color: #fff;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh;
  background-color: #fff;
  padding: 20px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.error-container h2 {
  margin-bottom: 10px;
  color: #e74c3c;
}

.error-container p {
  color: #666;
  max-width: 400px;
}
</style>
