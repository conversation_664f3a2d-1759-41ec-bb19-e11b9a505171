<template>
  <div
    class="image-element element"
    :class="{ 'selected': isSelected, 'dragging': isDragging, 'resizing': isResizing }"
    :style="containerStyle"
    @click.stop="handleSelect"
    @mousedown.stop="startDrag"
  >
    <img
      v-if="element.content.src"
      :src="element.content.src"
      :alt="element.content.alt"
      :style="imageStyle"
    />
    <div v-else class="placeholder" :style="placeholderStyle">
      이미지를 선택하세요
    </div>

    <!-- 리사이즈 핸들 -->
    <template v-if="isSelected">
      <div class="resize-handle top-left" @mousedown.stop="(e) => startResize(e, 'top-left')"></div>
      <div class="resize-handle top" @mousedown.stop="(e) => startResize(e, 'top')"></div>
      <div class="resize-handle top-right" @mousedown.stop="(e) => startResize(e, 'top-right')"></div>
      <div class="resize-handle right" @mousedown.stop="(e) => startResize(e, 'right')"></div>
      <div class="resize-handle bottom-right" @mousedown.stop="(e) => startResize(e, 'bottom-right')"></div>
      <div class="resize-handle bottom" @mousedown.stop="(e) => startResize(e, 'bottom')"></div>
      <div class="resize-handle bottom-left" @mousedown.stop="(e) => startResize(e, 'bottom-left')"></div>
      <div class="resize-handle left" @mousedown.stop="(e) => startResize(e, 'left')"></div>
    </template>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useDraggable } from '@/composables/useDraggable';
import { useResizable } from '@/composables/useResizable';

// 프롭스
const props = defineProps({
  element: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  }
});

// 이벤트
const emit = defineEmits(['select', 'update', 'positionUpdate']);

// 컨테이너 스타일
const containerStyle = computed(() => {
  const { position, size, style, zIndex } = props.element;

  // 위치 단위 처리
  const getPositionValue = (pos) => {
    if (!pos || !pos.value) return '0px';
    return pos.unit === 'px' ? `${pos.value}px` :
           pos.unit === '%' ? `${pos.value}%` :
           pos.unit === 'vw' ? `${pos.value}vw` :
           pos.unit === 'vh' ? `${pos.value}vh` :
           `${pos.value}px`;
  };

  // 크기 단위 처리
  const getSizeValue = (sz) => {
    if (!sz || !sz.value) return 'auto';
    if (sz.value === 'auto') return 'auto';
    return sz.unit === 'px' ? `${sz.value}px` :
           sz.unit === '%' ? `${sz.value}%` :
           sz.unit === 'vw' ? `${sz.value}vw` :
           sz.unit === 'vh' ? `${sz.value}vh` :
           `${sz.value}px`;
  };

  return {
    position: 'absolute',
    left: getPositionValue(position.x),
    top: getPositionValue(position.y),
    width: getSizeValue(size.width),
    height: getSizeValue(size.height),
    backgroundColor: style.backgroundColor || 'transparent',
    opacity: style.opacity,
    transform: style.rotation ? `rotate(${style.rotation}deg)` : 'none',
    borderWidth: `${style.borderWidth}px`,
    borderStyle: style.borderWidth > 0 ? (style.borderStyle || 'solid') : 'none',
    borderColor: style.borderColor || '#000000',
    borderRadius: `${style.borderRadius}px`,
    padding: style.padding || '0',
    margin: style.margin || '0',
    boxShadow: style.boxShadow || 'none',
    zIndex,
    overflow: 'hidden',
    cursor: 'pointer'
  };
});

// 이미지 스타일
const imageStyle = computed(() => {
  const { content } = props.element;

  return {
    width: '100%',
    height: '100%',
    objectFit: content.objectFit || 'contain',
    filter: content.filter || 'none'
  };
});

// 플레이스홀더 스타일 (높이가 작을 때 플레이스홀더 위치 조정)
const placeholderStyle = computed(() => {
  const { size } = props.element;

  // 높이 값 가져오기
  let height = 0;
  if (size && size.height) {
    if (typeof size.height === 'object' && size.height.value) {
      height = Number(size.height.value);
    } else if (typeof size.height === 'number') {
      height = size.height;
    }
  }

  // 높이가 20px 미만인 경우 플레이스홀더 위치 조정
  if (height < 20) {
    return {
      position: 'absolute',
      top: '20px',
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      padding: '2px 5px',
      borderRadius: '3px',
      fontSize: '12px',
      zIndex: 10,
      width: 'auto',
      height: 'auto'
    };
  }

  return {};
});

// 선택 핸들러
const handleSelect = () => {
  emit('select', props.element.id);
};

// 드래그 기능 사용
const { isDragging, startDrag } = useDraggable(props, emit, handleSelect);

// 리사이즈 기능 사용
const { isResizing, startResize } = useResizable(props, emit);
</script>

<style scoped>
.image-element {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 10px;
  min-height: 1px;
}

.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  color: #999;
  font-size: 14px;
  text-align: center;
}

.element.selected {
  outline: 2px solid #2196F3;
  outline-offset: 2px;
}

.element.dragging {
  opacity: 0.8;
  cursor: move;
  z-index: 9999 !important;
}

.element.resizing {
  opacity: 0.8;
  z-index: 9999 !important;
}

/* 리사이즈 핸들 스타일 */
.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #2196F3;
  border: 1px solid #fff;
  border-radius: 50%;
  z-index: 10;
}

.top-left {
  top: -4px;
  left: -4px;
  cursor: nwse-resize;
}

.top {
  top: -4px;
  left: calc(50% - 4px);
  cursor: ns-resize;
}

.top-right {
  top: -4px;
  right: -4px;
  cursor: nesw-resize;
}

.right {
  top: calc(50% - 4px);
  right: -4px;
  cursor: ew-resize;
}

.bottom-right {
  bottom: -4px;
  right: -4px;
  cursor: nwse-resize;
}

.bottom {
  bottom: -4px;
  left: calc(50% - 4px);
  cursor: ns-resize;
}

.bottom-left {
  bottom: -4px;
  left: -4px;
  cursor: nesw-resize;
}

.left {
  top: calc(50% - 4px);
  left: -4px;
  cursor: ew-resize;
}
</style>
