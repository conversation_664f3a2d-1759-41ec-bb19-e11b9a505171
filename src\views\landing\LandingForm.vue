<template>
  <div class="landing-form-container">
    <div class="editor-header">
      <h1>{{ isEditMode ? '랜딩 페이지 수정' : '랜딩 페이지 생성' }}</h1>

      <div class="header-actions">
        <button @click="openPreview" class="preview-btn" title="미리보기">
          <span class="btn-icon">🔍</span> 미리보기
        </button>
        <button @click="saveLandingPage" class="save-btn" :disabled="isSaving">
          {{ isSaving ? '저장 중...' : '저장' }}
        </button>
        <button @click="goBack" class="cancel-btn">취소</button>
      </div>
    </div>

    <div v-if="isLoading" class="loading">
      데이터를 불러오는 중...
    </div>

    <div v-else-if="error" class="error-message">
      {{ error }}
    </div>

    <div v-else class="editor-container">
      <!-- 템플릿 선택 화면 (생성 모드일 때만 표시) -->
      <div v-if="!isEditMode && showTemplateSelector" class="template-selector-container">
        <TemplateSelector @apply-template="handleTemplateApply" />
      </div>

      <!-- 에디터 화면 -->
      <div v-else>
        <!-- 기본 정보 입력 폼 -->
        <div class="basic-info-form">
          <div class="form-group">
            <label for="landingPageName">랜딩 페이지 이름</label>
            <input
              type="text"
              id="landingPageName"
              v-model="landingPageName"
              placeholder="랜딩 페이지 이름을 입력하세요"
              required
            />
          </div>

          <div class="form-group">
            <label for="description">설명</label>
            <textarea
              id="description"
              v-model="description"
              placeholder="랜딩 페이지에 대한 설명을 입력하세요"
              rows="3"
            ></textarea>
          </div>

          <div class="form-row">
            <div class="form-group half">
              <label for="validFromDate">유효 시작일</label>
              <input type="datetime-local" id="validFromDate" v-model="validFromDate" />
            </div>

            <div class="form-group half">
              <label for="validToDate">유효 종료일</label>
              <input type="datetime-local" id="validToDate" v-model="validToDate" />
            </div>
          </div>

          <div class="form-group">
            <label for="status">상태</label>
            <select id="status" v-model="status">
              <option value="ACTIVE">활성</option>
              <option value="INACTIVE">비활성</option>
            </select>
          </div>
        </div>

        <!-- 에디터 영역 -->
        <div class="editor-main">
        <div class="editor-toolbar">
          <button @click="undo" :disabled="!canUndo" class="toolbar-btn">
            <span class="icon">↩</span> 실행 취소
          </button>
          <button @click="redo" :disabled="!canRedo" class="toolbar-btn">
            <span class="icon">↪</span> 다시 실행
          </button>
          <div class="zoom-controls">
            <button @click="decreaseZoom" class="zoom-btn">-</button>
            <span class="zoom-level">{{ Math.round(zoom * 100) }}%</span>
            <button @click="increaseZoom" class="zoom-btn">+</button>
          </div>
        </div>

        <div class="editor-workspace">
          <!-- 왼쪽 패널: 요소 추가 및 요소 목록 -->
          <div class="editor-panel left-panel">
            <ElementPanel />
            <ElementList />
          </div>

          <!-- 중앙: 캔버스 -->
          <div class="editor-canvas-container" ref="canvasContainer">
            <Canvas :zoom="zoom" />
          </div>

          <!-- 오른쪽 패널: 속성 편집 -->
          <div class="editor-panel right-panel">
            <PropertyPanel />
          </div>
        </div>
        </div>
      </div>
    </div>

    <!-- 미리보기 모달 -->
    <PreviewModal
      v-if="showPreview"
      :show="showPreview"
      :landing-page="landingEditorStore.currentLandingPage"
      @close="closePreview"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useLandingEditorStore } from '@/stores/landingEditor';
import { getLandingPageById, createLandingPage, updateLandingPage } from '@/api/landing';

// 날짜 형식 변환 함수 - 'YYYY-MM-DDThh:mm' 형식을 'YYYY-MM-DD hh:mm:ss.SSS' 형식으로 변환
// 예: '2025-04-26T14:27' -> '2025-04-26 14:27:00.000'
const formatDateForServer = (dateString) => {
  if (!dateString) return null;

  try {
    // 입력된 날짜 문자열에서 날짜와 시간 부분 추출
    const [datePart, timePart] = dateString.split('T');

    // 시간 부분이 있는지 확인
    if (datePart && timePart) {
      // 시간 부분에 초 추가 (없는 경우)
      const timeWithSeconds = timePart.includes(':') ?
        (timePart.split(':').length === 2 ? `${timePart}:00` : timePart) :
        `${timePart}:00:00`;

      // 최종 형식으로 변환 (공백으로 구분하고 밀리초 추가)
      return `${datePart} ${timeWithSeconds}.000`;
    }

    // 날짜만 있는 경우 시간 추가
    return `${datePart} 00:00:00.000`;
  } catch (e) {
    console.error('날짜 형식 변환 오류:', e);
    return dateString; // 오류 발생 시 원본 문자열 반환
  }
};

// 컴포넌트 임포트
import Canvas from '@/components/landing/editor/Canvas.vue';
import ElementPanel from '@/components/landing/editor/ElementPanel.vue';
import PropertyPanel from '@/components/landing/editor/PropertyPanel.vue';
import ElementList from '@/components/landing/editor/ElementList.vue';
import PreviewModal from '@/components/landing/preview/PreviewModal.vue';
import TemplateSelector from '@/components/landing/template/TemplateSelector.vue';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const landingEditorStore = useLandingEditorStore();

// 상태
const isLoading = ref(false);
const error = ref(null);
const isSaving = ref(false);
const zoom = ref(1);
const showPreview = ref(false);
const showTemplateSelector = ref(!route.params.landingPageId); // 생성 모드일 때만 템플릿 선택기 표시

// 폼 데이터
const landingPageName = ref('');
const description = ref('');
const validFromDate = ref('');
const validToDate = ref('');
const status = ref('ACTIVE');

// 수정 모드 여부
const isEditMode = computed(() => {
  return !!route.params.landingPageId;
});

// 현재 선택된 프로젝트
const currentProject = computed(() => {
  return authStore.currentProject;
});

// 실행 취소/다시 실행 가능 여부
const canUndo = computed(() => landingEditorStore.canUndo);
const canRedo = computed(() => landingEditorStore.canRedo);

// 줌 제어 함수
const increaseZoom = () => {
  if (zoom.value < 2) {
    zoom.value = Math.min(2, zoom.value + 0.1);
  }
};

const decreaseZoom = () => {
  if (zoom.value > 0.5) {
    zoom.value = Math.max(0.5, zoom.value - 0.1);
  }
};

// 실행 취소/다시 실행 함수
const undo = () => {
  landingEditorStore.undo();
};

const redo = () => {
  landingEditorStore.redo();
};

// 미리보기 관련 함수
const openPreview = () => {
  // 현재 랜딩 페이지 데이터가 있는지 확인
  if (!landingEditorStore.currentLandingPage) {
    alert('미리보기할 랜딩 페이지 데이터가 없습니다.');
    return;
  }

  // 미리보기 모달 표시
  showPreview.value = true;
};

const closePreview = () => {
  // 미리보기 모달 닫기
  showPreview.value = false;
};

// URL 유효성 검사 함수
const isValidUrl = (url) => {
  return url && (url.startsWith('http://') || url.startsWith('https://'));
};

// 버튼과 링크 요소의 URL 유효성 검사
const validateElementUrls = () => {
  if (!landingEditorStore.currentLandingPage || !landingEditorStore.currentLandingPage.elements) {
    return { valid: true };
  }

  const elements = landingEditorStore.currentLandingPage.elements;
  const invalidElements = [];

  for (const element of elements) {
    // 버튼 요소 검사 (링크 타입인 경우만)
    if (element.type === 'button' &&
        element.content?.action?.type === 'link' &&
        element.content?.action?.target &&
        !isValidUrl(element.content.action.target)) {
      invalidElements.push({
        id: element.id,
        name: element.name,
        type: '버튼',
        url: element.content.action.target
      });
    }

    // 링크 요소 검사
    if (element.type === 'link' &&
        element.content?.url &&
        !isValidUrl(element.content.url)) {
      invalidElements.push({
        id: element.id,
        name: element.name,
        type: '링크',
        url: element.content.url
      });
    }
  }

  return {
    valid: invalidElements.length === 0,
    invalidElements
  };
};

// 랜딩 페이지 저장
const saveLandingPage = async () => {
  if (!landingPageName.value) {
    alert('랜딩 페이지 이름을 입력해주세요.');
    return;
  }

  if (!currentProject.value?.projectId) {
    alert('프로젝트를 선택해주세요.');
    return;
  }

  // URL 유효성 검사
  const urlValidation = validateElementUrls();
  if (!urlValidation.valid) {
    const invalidElementsInfo = urlValidation.invalidElements.map(el =>
      `- ${el.type} '${el.name}': ${el.url}`
    ).join('\n');

    alert(`다음 요소의 URL이 유효하지 않습니다. URL은 http:// 또는 https://로 시작해야 합니다.\n\n${invalidElementsInfo}`);
    return;
  }

  isSaving.value = true;
  error.value = null;

  try {
    // status 값 매핑 (ACTIVE -> PUBLISHED, INACTIVE -> DRAFT)
    const mappedStatus = status.value === 'ACTIVE' ? 'PUBLISHED' : 'DRAFT';

    // 에디터 데이터 가져오기
    const currentPage = landingEditorStore.currentLandingPage;

    // JSON 데이터 구성
    const contentJsonData = {
      canvas: currentPage.canvas,
      elements: currentPage.elements,
      description: description.value // description을 contentJson에도 포함
    };

    // 유효기간 날짜 형식 변환
    const formattedValidFromDate = validFromDate.value ? formatDateForServer(validFromDate.value) : null;
    const formattedValidToDate = validToDate.value ? formatDateForServer(validToDate.value) : null;

    // 서버에 보낼 데이터 구성
    const landingPageData = {
      projectId: currentProject.value.projectId,
      pageTitle: landingPageName.value,
      description: description.value,
      validFromDate: formattedValidFromDate, // 변환된 날짜 형식 사용
      validToDate: formattedValidToDate, // 변환된 날짜 형식 사용
      status: mappedStatus,
      contentJson: contentJsonData // contentJson을 객체 형태로 전송
    };

    if (isEditMode.value) {
      // 수정 모드
      await updateLandingPage(route.params.landingPageId, landingPageData);
      alert('랜딩 페이지가 성공적으로 수정되었습니다.');
    } else {
      // 생성 모드
      await createLandingPage(landingPageData);
      alert('랜딩 페이지가 성공적으로 생성되었습니다.');
    }

    // 목록 페이지로 이동
    router.push({ name: 'landing-management' });
  } catch (err) {
    console.error('랜딩 페이지 저장 오류:', err);
    error.value = err.message || '랜딩 페이지 저장 중 오류가 발생했습니다.';
  } finally {
    isSaving.value = false;
  }
};

// 목록으로 돌아가기
const goBack = () => {
  if (landingEditorStore.currentLandingPage && confirm('변경 사항이 저장되지 않을 수 있습니다. 계속하시겠습니까?')) {
    router.push({ name: 'landing-management' });
  } else if (!landingEditorStore.currentLandingPage) {
    router.push({ name: 'landing-management' });
  }
};

// 랜딩 페이지 데이터 로드
const loadLandingPage = async (landingPageId) => {
  isLoading.value = true;
  error.value = null;

  try {
    const response = await getLandingPageById(landingPageId);

    // 서버 응답 구조 확인 (success, data 필드)
    if (!response || !response.success || !response.data) {
      throw new Error('서버 응답 구조가 올바르지 않습니다.');
    }

    // 응답의 data 필드에서 랜딩 페이지 데이터 추출
    const landingPageData = response.data;

    // 폼 데이터 설정
    // pageTitle을 landingPageName으로 매핑
    landingPageName.value = landingPageData.pageTitle || '';
    description.value = landingPageData.description || '';

    // status 값 매핑 (PUBLISHED -> ACTIVE, DRAFT -> INACTIVE)
    status.value = landingPageData.status === 'PUBLISHED' ? 'ACTIVE' : 'INACTIVE';

    // 서버에서 받은 날짜 형식을 HTML datetime-local 입력에 맞게 변환
    // 'YYYY-MM-DD hh:mm:ss.SSS' -> 'YYYY-MM-DDThh:mm'
    if (landingPageData.validFromDate) {
      try {
        // 날짜 문자열이 'T'를 포함하는 ISO 형식인 경우 (2025-04-26T14:27:00)
        if (landingPageData.validFromDate.includes('T')) {
          // 초와 밀리초 부분 제거
          const [datePart, timePart] = landingPageData.validFromDate.split('T');
          const timeWithoutSeconds = timePart.split(':').slice(0, 2).join(':');
          validFromDate.value = `${datePart}T${timeWithoutSeconds}`;
        }
        // 공백으로 구분된 날짜와 시간 부분 추출 (2025-04-26 14:27:00)
        else if (landingPageData.validFromDate.includes(' ')) {
          const [datePart, timePart] = landingPageData.validFromDate.split(' ');
          if (datePart && timePart) {
            // 시간 부분에서 초와 밀리초 제거
            const timeWithoutSeconds = timePart.split(':').slice(0, 2).join(':');
            validFromDate.value = `${datePart}T${timeWithoutSeconds}`;
          } else {
            validFromDate.value = landingPageData.validFromDate;
          }
        } else {
          validFromDate.value = landingPageData.validFromDate;
        }
      } catch (e) {
        console.warn('유효 시작일 형식 변환 오류:', e);
        validFromDate.value = landingPageData.validFromDate;
      }
    } else {
      validFromDate.value = '';
    }

    if (landingPageData.validToDate) {
      try {
        // 날짜 문자열이 'T'를 포함하는 ISO 형식인 경우 (2025-04-26T14:27:00)
        if (landingPageData.validToDate.includes('T')) {
          // 초와 밀리초 부분 제거
          const [datePart, timePart] = landingPageData.validToDate.split('T');
          const timeWithoutSeconds = timePart.split(':').slice(0, 2).join(':');
          validToDate.value = `${datePart}T${timeWithoutSeconds}`;
        }
        // 공백으로 구분된 날짜와 시간 부분 추출 (2025-04-26 14:27:00)
        else if (landingPageData.validToDate.includes(' ')) {
          const [datePart, timePart] = landingPageData.validToDate.split(' ');
          if (datePart && timePart) {
            // 시간 부분에서 초와 밀리초 제거
            const timeWithoutSeconds = timePart.split(':').slice(0, 2).join(':');
            validToDate.value = `${datePart}T${timeWithoutSeconds}`;
          } else {
            validToDate.value = landingPageData.validToDate;
          }
        } else {
          validToDate.value = landingPageData.validToDate;
        }
      } catch (e) {
        console.warn('유효 종료일 형식 변환 오류:', e);
        validToDate.value = landingPageData.validToDate;
      }
    } else {
      validToDate.value = '';
    }

    // contentJson 처리
    let contentJsonData = null;

    if (landingPageData.contentJson) {
      try {
        // 문자열인 경우 파싱
        if (typeof landingPageData.contentJson === 'string') {
          contentJsonData = JSON.parse(landingPageData.contentJson);
        }
        // 이미 객체인 경우 그대로 사용
        else if (typeof landingPageData.contentJson === 'object') {
          contentJsonData = landingPageData.contentJson;
        }
      } catch (e) {
        console.error('contentJson 처리 오류:', e);
        contentJsonData = null;
      }
    }
    // 이전 버전의 content_json 필드 확인 (하위 호환성)
    else if (landingPageData.content_json) {
      contentJsonData = landingPageData.content_json;
    }

    // contentJsonData가 없으면 기본 객체 생성
    if (!contentJsonData) {
      contentJsonData = {
        canvas: {
          width: 375,
          height: 667,
          backgroundColor: '#ffffff',
          backgroundImage: null,
          backgroundFit: 'cover'
        },
        elements: []
      };
    }

    // 에디터 스토어에 데이터 로드
    if (contentJsonData && (contentJsonData.canvas || contentJsonData.elements)) {
      landingEditorStore.loadLandingPage({
        ...landingPageData,
        canvas: contentJsonData.canvas,
        elements: contentJsonData.elements,
        settings: contentJsonData.settings || {}
      });
    } else {
      // 에디터 데이터가 없는 경우 기본 데이터 생성
      landingEditorStore.createNewLandingPage(
        currentProject.value?.projectId,
        landingPageData.pageTitle || '새 랜딩 페이지'
      );
    }
  } catch (err) {
    console.error('랜딩 페이지 로드 오류:', err);
    error.value = err.message || '랜딩 페이지 데이터를 불러오는 중 오류가 발생했습니다.';

    // 에러 발생 시 기본 데이터 생성
    landingEditorStore.createNewLandingPage(
      currentProject.value?.projectId,
      '새 랜딩 페이지'
    );
  } finally {
    isLoading.value = false;
  }
};

// 템플릿 적용 핸들러
const handleTemplateApply = (template) => {
  // 템플릿이 있는 경우 템플릿 적용, 없는 경우 빈 페이지 생성
  landingEditorStore.createNewLandingPage(
    currentProject.value?.projectId,
    template ? template.name : '새 랜딩 페이지',
    template
  );

  // 템플릿 선택기 닫기
  showTemplateSelector.value = false;
};

// 컴포넌트 마운트 시 필요한 데이터 로드
onMounted(() => {
  if (isEditMode.value) {
    // 수정 모드인 경우 데이터 로드
    loadLandingPage(route.params.landingPageId);
    showTemplateSelector.value = false; // 수정 모드에서는 템플릿 선택기 표시 안함
  }
  // 생성 모드인 경우 템플릿 선택기 표시 (새 랜딩 페이지는 템플릿 선택 후 생성)
});

// 프로젝트 변경 감지
watch(() => currentProject.value?.projectId, (newProjectId, oldProjectId) => {
  if (newProjectId && newProjectId !== oldProjectId) {
    if (!isEditMode.value) {
      // 생성 모드에서 프로젝트가 변경된 경우 템플릿 선택기 표시
      showTemplateSelector.value = true;
    } else {
      // 수정 모드에서 프로젝트가 변경된 경우 에디터 데이터 초기화
      landingEditorStore.createNewLandingPage(
        newProjectId,
        landingPageName.value || '새 랜딩 페이지'
      );
    }
  }
});
</script>

<style scoped>
.landing-form-container {
  padding: 20px;
  width: 100%;
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.preview-btn, .save-btn, .cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 6px;
}

.preview-btn {
  background-color: #2196F3;
  color: white;
}

.preview-btn:hover {
  background-color: #0b7dda;
}

.btn-icon {
  font-size: 16px;
}

.save-btn {
  background-color: #4CAF50;
  color: white;
}

.save-btn:hover:not(:disabled) {
  background-color: #45a049;
}

.save-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.loading, .error-message {
  padding: 20px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-top: 20px;
}

.error-message {
  color: #f44336;
  background-color: #ffebee;
}

.editor-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 20px;
  height: 100%;
}

.basic-info-form {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: bold;
  color: #555;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-group.half {
  flex: 1;
}

.editor-main {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #e0e0e0;
  border-bottom: 1px solid #ccc;
}

.toolbar-btn {
  padding: 6px 12px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-btn:hover:not(:disabled) {
  background-color: #f0f0f0;
}

/* 템플릿 선택기 스타일 */
.template-selector-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: auto;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-btn .icon {
  font-size: 16px;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-btn {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
}

.zoom-btn:hover {
  background-color: #f0f0f0;
}

.zoom-level {
  font-size: 13px;
  min-width: 40px;
  text-align: center;
}

.editor-workspace {
  display: flex;
  flex-grow: 1;
  height: calc(100% - 40px);
  overflow: hidden;
}

.editor-panel {
  flex: 0 0 300px;
  overflow-y: auto;
  background-color: #fff;
  border-right: 1px solid #ddd;
  padding: 16px;
}

.right-panel {
  border-right: none;
  border-left: 1px solid #ddd;
}

.editor-canvas-container {
  flex-grow: 1;
  overflow: auto;
  background-color: #e9e9e9;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
}
</style>
