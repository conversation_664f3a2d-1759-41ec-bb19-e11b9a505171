import { ref, onUnmounted, inject } from 'vue';
import { useLandingEditorStore } from '@/stores/landingEditor';

/**
 * 요소 드래그 기능을 제공하는 컴포저블 함수
 * @param {Object} props - 컴포넌트 props
 * @param {Function} emit - 컴포넌트 emit 함수
 * @param {Function} handleSelect - 요소 선택 핸들러 함수
 * @returns {Object} 드래그 관련 상태 및 함수
 */
export function useDraggable(props, emit, handleSelect) {
  // 랜딩 에디터 스토어
  const landingEditorStore = useLandingEditorStore();

  // 작업 그룹화 함수 안전하게 호출하는 헬퍼 함수
  const safeStartGrouping = () => {
    if (landingEditorStore && typeof landingEditorStore.startGroupingOperations === 'function') {
      landingEditorStore.startGroupingOperations();
    } else {
      console.warn('startGroupingOperations 함수를 찾을 수 없습니다.');
    }
  };

  const safeEndGrouping = () => {
    if (landingEditorStore && typeof landingEditorStore.endGroupingOperations === 'function') {
      landingEditorStore.endGroupingOperations();
    } else {
      console.warn('endGroupingOperations 함수를 찾을 수 없습니다.');
    }
  };
  // 드래그 관련 상태
  const isDragging = ref(false);
  const startX = ref(0);
  const startY = ref(0);
  const originalX = ref(0);
  const originalY = ref(0);

  // 기본값 근처인지 확인하는 함수 (드래그 시작 시에만 사용)
  const isNearDefaultValue = (value) => {
    // 95px ~ 105px 범위를 100px 근처로 간주
    if (value >= 95 && value <= 105) return true;

    // 45px ~ 55px 범위를 50px 근처로 간주
    if (value >= 45 && value <= 55) return true;

    return false;
  };

  // 드래그 중인지 확인하는 변수 (드래그 시작 후 일정 시간 동안은 기본값 근처 감지 로직 비활성화)
  const isDraggingStarted = ref(false);

  // 드래그 시작 핸들러
  const startDrag = (event) => {
    // 이미 선택되지 않았다면 선택
    if (!props.isSelected) {
      handleSelect();
    }

    // 요소 선택 작업이 실행 취소에 포함되지 않도록 작업 그룹화 시작
    safeStartGrouping();

    // 드래그 시작 위치 저장
    isDragging.value = true;
    isDraggingStarted.value = true; // 드래그 시작 표시
    startX.value = event.clientX;
    startY.value = event.clientY;

    // 원래 요소 위치 저장 (현재 값 가져오기)
    try {
      // position.x가 객체인지 확인
      if (props.element.position.x && typeof props.element.position.x === 'object') {
        const xValue = Number(props.element.position.x.value) || 0;

        // 기본값 근처인지 확인
        if (isNearDefaultValue(xValue)) {
          console.warn('드래그 시작 - x 위치가 기본값 근처(' + xValue + 'px)입니다.');
        }
        originalX.value = xValue;
      } else {
        // 예전 형식의 데이터일 경우
        const xValue = Number(props.element.position.x) || 0;

        if (isNearDefaultValue(xValue)) {
          console.warn('드래그 시작 - x 위치가 기본값 근처(' + xValue + 'px)입니다.');
        }
        originalX.value = xValue;
      }

      // position.y가 객체인지 확인
      if (props.element.position.y && typeof props.element.position.y === 'object') {
        const yValue = Number(props.element.position.y.value) || 0;

        // 기본값 근처인지 확인
        if (isNearDefaultValue(yValue)) {
          console.warn('드래그 시작 - y 위치가 기본값 근처(' + yValue + 'px)입니다.');
        }
        originalY.value = yValue;
      } else {
        // 예전 형식의 데이터일 경우
        const yValue = Number(props.element.position.y) || 0;

        if (isNearDefaultValue(yValue)) {
          console.warn('드래그 시작 - y 위치가 기본값 근처(' + yValue + 'px)입니다.');
        }
        originalY.value = yValue;
      }

    } catch (error) {
      console.error('위치 가져오기 오류:', error);
      originalX.value = 0; // 오류 발생 시 기본값으로 0px 사용
      originalY.value = 0;
    }

    // 전역 이벤트 리스너 추가
    document.addEventListener('mousemove', handleDrag);
    document.addEventListener('mouseup', stopDrag);

    // 이벤트 전파 중지 및 기본 동작 방지
    event.stopPropagation();
    event.preventDefault();
  };

  // 드래그 중 핸들러
  const handleDrag = (event) => {
    if (!isDragging.value) return;


    // 이동 거리 계산
    const deltaX = event.clientX - startX.value;
    const deltaY = event.clientY - startY.value;

    // 너무 작은 움직임은 무시 (실수로 인한 미세한 움직임 방지)
    // 값을 더 작게 설정하여 더 민감하게 반응하도록 함
    if (Math.abs(deltaX) < 0.5 && Math.abs(deltaY) < 0.5) {
      return;
    }

    // 새 위치 계산 (단위 고려) - 반올림 제거하여 더 부드럽게 이동하도록 함
    let newX = originalX.value + deltaX;
    let newY = originalY.value + deltaY;

    // 위치가 음수가 되지 않도록 방지
    newX = Math.max(0, newX);
    newY = Math.max(0, newY);

    // 기본값 근처 감지
    if (isNearDefaultValue(newX)) {
      console.warn('드래그 중 - x 위치가 기본값 근처(' + newX + 'px)입니다.');
    }
    if (isNearDefaultValue(newY)) {
      console.warn('드래그 중 - y 위치가 기본값 근처(' + newY + 'px)입니다.');
    }

    // 위치 업데이트 이벤트 발생
    emit('positionUpdate', props.element.id, newX, newY);

    // 이벤트 전파 중지 및 기본 동작 방지
    event.stopPropagation();
    event.preventDefault();
  };

  // 드래그 종료 핸들러
  const stopDrag = (event) => {
    if (!isDragging.value) return;

    isDragging.value = false;
    isDraggingStarted.value = false; // 드래그 종료 표시

    // 작업 그룹화 종료 - 드래그 종료 시 상태 저장
    safeEndGrouping();

    // 전역 이벤트 리스너 제거
    document.removeEventListener('mousemove', handleDrag);
    document.removeEventListener('mouseup', stopDrag);

    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }
  };

  // 컴포넌트 마운트 시 이벤트 리스너 정리
  onUnmounted(() => {
    document.removeEventListener('mousemove', handleDrag);
    document.removeEventListener('mouseup', stopDrag);
  });

  return {
    isDragging,
    startDrag
  };
}
