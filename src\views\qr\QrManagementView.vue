<template>
  <div class="qr-management">
    <h1>QR 코드 관리</h1>

    <!-- 프로젝트가 없고 SUPER_ADMIN이 아닌 경우 -->
    <div v-if="!currentProject && authStore.user?.roleId !== 'SUPER_ADMIN'" class="no-projects-message">
      <p>선택된 프로젝트가 없습니다. 프로젝트를 선택해주세요.</p>
    </div>

    <!-- 프로젝트가 있거나 SUPER_ADMIN인 경우 QR 코드 목록 표시 -->
    <div v-else class="qr-list-container">
      <div class="filters">
        <div class="filter-group">
          <button @click="navigateToCreateQr" class="create-btn">새 QR 코드 추가</button>
          
          <!-- 페이지당 항목 수 선택 UI -->
          <div class="items-per-page-selector">
            <span>페이지당 항목:</span>
            <div class="items-per-page-buttons">
              <button 
                @click="changeItemsPerPage(10)" 
                :class="['item-count-btn', itemsPerPage === 10 ? 'active' : '']">
                10개
              </button>
              <button 
                @click="changeItemsPerPage(30)" 
                :class="['item-count-btn', itemsPerPage === 30 ? 'active' : '']">
                30개
              </button>
              <button 
                @click="changeItemsPerPage(50)" 
                :class="['item-count-btn', itemsPerPage === 50 ? 'active' : '']">
                50개
              </button>
            </div>
          </div>
        </div>
        <div class="filter-group">
          <label for="searchTypeSelect">검색 유형:</label>
          <select id="searchTypeSelect" v-model="searchType">
            <option v-for="type in availableSearchTypes.length > 0 ? availableSearchTypes : searchTypes" :key="type.value" :value="type.value">
              {{ type.label }}
            </option>
          </select>

          <label for="searchInput">검색어:</label>
          <input
            id="searchInput"
            type="text"
            v-model="searchQuery"
            placeholder="검색어 입력"
            @input="handleSearch"
            @keyup.enter="searchQrCodes"
          />

          <button @click="searchQrCodes" class="search-btn">검색</button>
          <button @click="resetSearch" class="reset-btn">초기화</button>
        </div>
      </div>

      <!-- 일괄 작업 버튼 -->
      <div v-if="filteredQrCodes.length > 0" class="bulk-actions">
        <div class="selected-count">
          {{ selectedQrCodeIds.length > 0 ? `${selectedQrCodeIds.length}개 선택됨` : '항목을 선택하세요' }}
        </div>
        <div class="action-buttons">
          <button
            @click="bulkDeleteQrCodes"
            class="bulk-action-btn delete-btn"
            :disabled="selectedQrCodeIds.length === 0 || isDeleting"
          >
            {{ isMultipleDeleting ? '삭제 중...' : '선택한 QR 코드 삭제' }}
          </button>
          <button
            @click="bulkDownloadQrCodes"
            class="bulk-action-btn download-btn"
            :disabled="selectedQrCodeIds.length === 0 || isDownloading"
          >
            {{ isDownloading ? '다운로드 중...' : '선택한 QR 코드 다운로드' }}
          </button>
        </div>
      </div>

      <div class="qr-table-container">
        <table v-if="filteredQrCodes.length > 0" class="qr-table">
          <thead>
            <tr>
              <th class="checkbox-column">
                <input
                  type="checkbox"
                  :checked="selectedQrCodeIds.length === filteredQrCodes.length && filteredQrCodes.length > 0"
                  :indeterminate.prop="selectedQrCodeIds.length > 0 && selectedQrCodeIds.length < filteredQrCodes.length"
                  @change="toggleSelectAll"
                />
              </th>
              <th>번호</th>
              <th @click="sortBy('qrName')">QR 이름 <span v-if="sortKey === 'qrName'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('qrType')">QR 타입 <span v-if="sortKey === 'qrType'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th>콘텐츠</th>
              <th>미리보기</th>
              <th @click="sortBy('status')">상태 <span v-if="sortKey === 'status'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('scanCount')">스캔 수 <span v-if="sortKey === 'scanCount'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('createDate')">생성일 <span v-if="sortKey === 'createDate'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('validFromDate')">유효 시작일 <span v-if="sortKey === 'validFromDate'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('validToDate')">유효 종료일 <span v-if="sortKey === 'validToDate'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th>기능</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(qrCode, index) in filteredQrCodes" :key="qrCode.qrCodeId">
              <td class="checkbox-column">
                <input
                  type="checkbox"
                  :checked="selectedQrCodeIds.includes(qrCode.qrCodeId)"
                  @change="toggleSelectQrCode(qrCode.qrCodeId)"
                  :disabled="!qrCode.imageUrl"
                />
              </td>
              <td>{{ calculateIndex(index) }}</td>
              <td>{{ qrCode.qrName }}</td>
              <td>{{ formatQrType(qrCode.qrType) }}</td>
              <td class="target-content">{{ truncateText(qrCode.targetContent, 30) }}</td>
              <td class="qr-preview-cell">
                <div v-if="qrCode.imageUrl" class="qr-preview-container">
                  <img :src="qrCode.imageUrl" alt="QR 코드 미리보기" class="qr-preview-thumbnail" />
                  <div class="qr-preview-modal">
                    <img :src="qrCode.imageUrl" alt="QR 코드 확대" class="qr-preview-large" />
                  </div>
                </div>
                <span v-else>-</span>
              </td>
              <td>{{ formatStatus(qrCode.status) }}</td>
              <td>{{ qrCode.scanCount }}</td>
              <td>{{ formatDate(qrCode.createDate) }}</td>
              <td>{{ formatDate(qrCode.validFromDate) }}</td>
              <td>{{ formatDate(qrCode.validToDate) }}</td>
              <td>
                <button @click="viewQrCode(qrCode.qrCodeId)" class="action-btn view-btn">보기</button>
                <button @click="editQrCode(qrCode.qrCodeId)" class="action-btn edit-btn">수정</button>
                <button @click="confirmDeleteQrCode(qrCode)" class="action-btn delete-btn">삭제</button>
                <button @click="downloadQrCode(qrCode)" class="action-btn download-btn" v-if="qrCode.imageUrl">다운로드</button>
              </td>
            </tr>
          </tbody>
        </table>

        <div v-else-if="isLoading" class="loading-message">
          <div class="loading-spinner"></div>
          <p>QR 코드 정보를 불러오는 중입니다...</p>
        </div>

        <div v-else-if="error" class="error-message">
          <p>{{ error }}</p>
        </div>

        <div v-else class="no-data-message">
          <p>QR 코드 데이터가 없습니다.</p>
        </div>
      </div>

      <!-- 페이지네이션 -->
      <div class="pagination" v-if="qrCodes.length > 0">
        <button
          @click="goToPage(0)"
          :disabled="currentPage === 0"
          class="pagination-btn"
        >
          &laquo;
        </button>
        <button
          @click="goToPage(currentPage - 1)"
          :disabled="currentPage === 0"
          class="pagination-btn"
        >
          &lt;
        </button>

        <span class="page-info">{{ displayPage }} / {{ totalPages || 1 }}</span>

        <button
          @click="goToPage(currentPage + 1)"
          :disabled="currentPage >= (totalPages - 1) || totalPages <= 1"
          class="pagination-btn"
        >
          &gt;
        </button>
        <button
          @click="goToPage(totalPages - 1)"
          :disabled="currentPage >= (totalPages - 1) || totalPages <= 1"
          class="pagination-btn"
        >
          &raquo;
        </button>
      </div>
    </div>

    <!-- 삭제 확인 모달 -->
    <div v-if="showDeleteModal" class="modal-overlay">
      <div class="modal-content">
        <h3>QR 코드 삭제 확인</h3>
        <p><strong>{{ selectedQrCode?.qrName }}</strong> QR 코드를 삭제하시겠습니까?</p>
        <p class="warning">이 작업은 되돌릴 수 없습니다.</p>
        <div class="modal-actions">
          <button @click="handleDeleteQrCode" class="confirm-btn" :disabled="isDeleting">
            {{ isDeleting ? '삭제 중...' : '삭제' }}
          </button>
          <button @click="cancelDelete" class="cancel-btn">취소</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { getQrCodes, getAllQrCodes, deleteQrCode, deleteMultipleQrCodes } from '@/api/qrcode';
import { handleApiError } from '@/utils/errorHandler';
import { useAuthStore } from '@/stores/auth';
import apiClient from '@/api/index';

const router = useRouter();
const authStore = useAuthStore();
const qrCodes = ref([]);
const isLoading = ref(true);
const error = ref(null);
const searchQuery = ref('');
const appliedSearchQuery = ref(''); // 실제 검색에 적용된 검색어
const searchType = ref('qrName'); // 기본 검색 타입 설정
const appliedSearchType = ref('qrName'); // 실제 검색에 적용된 검색 타입
const availableSearchTypes = ref([]); // 서버에서 제공하는 검색 유형 목록

// 기본 검색 유형 목록 (서버에서 제공하지 않을 경우 사용)
const searchTypes = [
  { value: 'qrName', label: 'QR 이름' },
  { value: 'targetContent', label: '콘텐츠' },
  { value: 'qrType', label: 'QR 타입' },
  { value: 'createDate', label: '생성일' },
  { value: 'validFromDate', label: '유효 시작일' },
  { value: 'validToDate', label: '유효 종료일' }
];
const showDeleteModal = ref(false);
const selectedQrCode = ref(null);
const isDeleting = ref(false);
const isMultipleDeleting = ref(false);
const selectedQrCodeIds = ref([]); // 선택된 QR 코드 ID 배열
const isDownloading = ref(false); // 다운로드 중 상태

// 페이지네이션 관련 상태
const currentPage = ref(0); // 서버 페이지네이션은 0부터 시작
const itemsPerPage = ref(10); // 페이지당 항목 수
const totalElements = ref(0); // 전체 항목 수
const totalPages = ref(0); // 전체 페이지 수

// 페이지 번호 표시용 (1부터 시작하는 페이지 번호)
const displayPage = computed(() => {
  return currentPage.value + 1;
});

// 인덱스 계산 함수
const calculateIndex = (index) => {
  // 마지막 번호부터 시작하도록 변경
  if (typeof totalElements.value !== 'number' || isNaN(totalElements.value)) {
    return index + 1; // 기본값으로 인덱스 + 1 반환
  }
  
  const result = totalElements.value - (currentPage.value * itemsPerPage.value + index);
  return isNaN(result) ? index + 1 : result;
};

// 정렬 관련 상태
const sortKey = ref('qrCodeId');
const sortOrder = ref('desc');

// 현재 선택된 프로젝트
const currentProject = computed(() => {
  return authStore.currentProject;
});

// 페이지당 항목 수 변경 함수
const changeItemsPerPage = (count) => {
  if (itemsPerPage.value !== count) {
    itemsPerPage.value = count;
    currentPage.value = 0; // 페이지를 첫 페이지로 리셋
    fetchQrCodes(); // 서버에 새로운 요청 보내기
  }
};

// 프로젝트 ID 변경 감지
watch(() => currentProject.value?.projectId, (newProjectId, oldProjectId) => {
  if (newProjectId && newProjectId !== oldProjectId) {
    fetchQrCodes();
  }
});

// 검색 결과 필터링
const filteredQrCodes = computed(() => {
  if (!appliedSearchQuery.value) {
    return sortedQrCodes.value;
  }

  const query = appliedSearchQuery.value.toLowerCase();

  return sortedQrCodes.value.filter(qrCode => {
    // 검색 타입에 따라 다른 필드 검색
    switch (appliedSearchType.value) {
      case 'qrName':
        return qrCode.qrName && qrCode.qrName.toLowerCase().includes(query);
      case 'targetContent':
        return qrCode.targetContent && qrCode.targetContent.toLowerCase().includes(query);
      case 'qrType':
        return qrCode.qrType && qrCode.qrType.toLowerCase().includes(query);
      case 'createDate':
        return qrCode.createDate && qrCode.createDate.toLowerCase().includes(query);
      case 'validFromDate':
        return qrCode.validFromDate && qrCode.validFromDate.toLowerCase().includes(query);
      case 'validToDate':
        return qrCode.validToDate && qrCode.validToDate.toLowerCase().includes(query);
      default:
        // 기본적으로 QR 이름과 콘텐츠 검색
        return (qrCode.qrName && qrCode.qrName.toLowerCase().includes(query)) ||
               (qrCode.targetContent && qrCode.targetContent.toLowerCase().includes(query));
    }
  });
});

// 정렬된 QR 코드 목록
const sortedQrCodes = computed(() => {
  const sortedArray = [...qrCodes.value];

  sortedArray.sort((a, b) => {
    let aValue = a[sortKey.value];
    let bValue = b[sortKey.value];

    // 날짜 형식 처리
    if (sortKey.value.includes('Date') && aValue && bValue) {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    // 문자열 처리
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    // null 또는 undefined 처리
    if (aValue === null || aValue === undefined) return sortOrder.value === 'asc' ? -1 : 1;
    if (bValue === null || bValue === undefined) return sortOrder.value === 'asc' ? 1 : -1;

    // 정렬 순서에 따라 비교
    if (sortOrder.value === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  return sortedArray;
});

// 정렬 함수
const sortBy = (key) => {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortKey.value = key;
    sortOrder.value = 'asc';
  }
};

// QR 코드 타입 포맷팅
const formatQrType = (type) => {
  const typeMap = {
    'URL': 'URL',
    'TEXT': '텍스트',
    'VCARD': '연락처',
    'EMAIL': '이메일',
    'SNS': 'SNS',
    'WIFI': 'Wi-Fi',
    'GEO': '위치',
    'LANDING_PAGE': '랜딩페이지',
    'EVENT': '이벤트',
    'EVENT_ATTENDANCE': '사전 신청',
    'LOCATION': '위치'
  };
  return typeMap[type] || type;
};

// 상태 포맷팅
const formatStatus = (status) => {
  const statusMap = {
    'ACTIVE': '활성',
    'INACTIVE': '비활성',
    'EXPIRED': '만료됨',
    'DELETED': '삭제됨'
  };
  return statusMap[status] || status;
};

const formatDate = (dateString) => {
  if (!dateString) return '-';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;

  return new Intl.DateTimeFormat('ko-KR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// 텍스트 길이 제한 함수
const truncateText = (text, maxLength) => {
  if (!text) return '-';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

// QR 코드 생성 페이지로 이동
const navigateToCreateQr = () => {
  // SUPER_ADMIN인 경우 프로젝트 ID 없이도 QR 코드 생성 가능
  const query = {};

  // 프로젝트 ID가 있는 경우에만 query에 추가
  if (currentProject.value?.projectId) {
    query.projectId = currentProject.value.projectId;
  }

  router.push({
    name: 'qr-form',
    query
  });
};

// QR 코드 상세 보기
const viewQrCode = (qrCodeId) => {
  // SUPER_ADMIN인 경우 프로젝트 ID 없이도 QR 코드 상세 보기 가능
  const query = {};

  // 프로젝트 ID가 있는 경우에만 query에 추가
  if (currentProject.value?.projectId) {
    query.projectId = currentProject.value.projectId;
  }

  router.push({
    name: 'qr-view',
    params: { qrCodeId: qrCodeId.toString() },
    query
  });
};

// QR 코드 수정 페이지로 이동
const editQrCode = (qrCodeId) => {
  // SUPER_ADMIN인 경우 프로젝트 ID 없이도 QR 코드 수정 가능
  const query = {};

  // 프로젝트 ID가 있는 경우에만 query에 추가
  if (currentProject.value?.projectId) {
    query.projectId = currentProject.value.projectId;
  }

  router.push({
    name: 'qr-form',
    params: { qrCodeId: qrCodeId.toString() },
    query
  });
};

// 삭제 확인 모달 표시
const confirmDeleteQrCode = (qrCode) => {
  selectedQrCode.value = qrCode;
  showDeleteModal.value = true;
};

// 삭제 취소
const cancelDelete = () => {
  showDeleteModal.value = false;
  selectedQrCode.value = null;
};

// QR 코드 삭제 처리
const handleDeleteQrCode = async () => {
  if (!selectedQrCode.value) return;

  isDeleting.value = true;

  try {
    // 삭제 API 호출
    await deleteQrCode(selectedQrCode.value.qrCodeId);

    // 성공 시 목록에서 제거
    qrCodes.value = qrCodes.value.filter(qr => qr.qrCodeId !== selectedQrCode.value.qrCodeId);

    // 모달 닫기
    showDeleteModal.value = false;
    selectedQrCode.value = null;

    // 성공 메시지
    alert('QR 코드가 성공적으로 삭제되었습니다.');
  } catch (err) {
    error.value = handleApiError(err, 'QR 코드 삭제 중 오류가 발생했습니다.');
  } finally {
    isDeleting.value = false;
  }
};

// 날짜 관련 검색 타입인지 확인하는 함수
const isDateSearchType = (type) => {
  // 날짜 관련 필드명 목록
  const dateFields = ['createDate', 'validFromDate', 'validToDate'];
  return dateFields.includes(type);
};

// 날짜 형식 변환 함수 (2025. 05. 17. -> 2025-05-17)
const formatDateForSearch = (dateString) => {
  if (!dateString) return '';

  // 정규식을 사용하여 날짜 형식 변환
  // 2025. 05. 17. 또는 2025.05.17. 또는 2025.5.17. 등의 형식을 처리
  const dateMatch = dateString.match(/(\d{4})[.\s-]*(\d{1,2})[.\s-]*(\d{1,2})/);

  if (dateMatch) {
    const year = dateMatch[1];
    // 월과 일이 한 자리 수인 경우 앞에 0을 추가
    const month = dateMatch[2].padStart(2, '0');
    const day = dateMatch[3].padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // 매칭되지 않으면 원래 문자열 반환
  return dateString;
};

// 검색 버튼 클릭 시 실행되는 함수
const searchQrCodes = () => {
  currentPage.value = 0; // 검색 시 첫 페이지로 이동

  // 날짜 관련 검색 타입인 경우 날짜 형식 변환
  if (searchType.value && isDateSearchType(searchType.value) && searchQuery.value) {
    // 원본 검색어 저장
    const originalQuery = searchQuery.value;
    // 변환된 날짜 형식으로 검색어 업데이트
    const formattedDate = formatDateForSearch(originalQuery);

    if (formattedDate !== originalQuery) {
      searchQuery.value = formattedDate;
    }
  }

  // 검색 쿼리와 타입을 적용
  appliedSearchQuery.value = searchQuery.value;
  appliedSearchType.value = searchType.value;

};

// 검색 초기화 함수
const resetSearch = () => {
  searchQuery.value = '';
  appliedSearchQuery.value = '';
  // 서버에서 제공하는 검색 유형이 있으면 첫 번째 값을 사용, 없으면 기본값 사용
  if (availableSearchTypes.value.length > 0) {
    searchType.value = availableSearchTypes.value[0].value;
    appliedSearchType.value = availableSearchTypes.value[0].value;
  } else {
    searchType.value = 'qrName';
    appliedSearchType.value = 'qrName';
  }
  currentPage.value = 0;
  fetchQrCodes();
};

// 검색 처리 (디바운스 적용)
let searchTimeout;
const handleSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    // 검색어가 비어있을 때는 아무 동작도 하지 않음
    // 사용자가 직접 검색 버튼을 클릭하거나 Enter 키를 눌러야 데이터가 로드됨
    // 검색어 입력 중에는 화면이 바뀌지 않음
  }, 300);
};

// 페이지 이동 함수
const goToPage = (page) => {
  // 페이지 범위 검사 (totalPages가 0이면 최소 1페이지로 간주)
  const maxPage = Math.max(totalPages.value - 1, 0);
  if (page < 0 || page > maxPage) {
    console.warn(`페이지 범위 초과: ${page}, 최대 페이지: ${maxPage}`);
    return;
  }

  // 페이지 변경 및 데이터 로드
  currentPage.value = page;
  fetchQrCodes();

  // 상단으로 스크롤
  window.scrollTo(0, 0);
};

// QR 코드 목록 불러오기
const fetchQrCodes = async () => {
  // SUPER_ADMIN인 경우 프로젝트 ID 없이도 모든 QR 코드를 가져올 수 있음
  const isSuperAdmin = authStore.user?.roleId === 'SUPER_ADMIN';

  // SUPER_ADMIN이 아니고 프로젝트 ID가 없는 경우 데이터를 가져오지 않음
  if (!isSuperAdmin && !currentProject.value?.projectId) {
    qrCodes.value = [];
    return;
  }

  isLoading.value = true;
  error.value = null;

  try {
    let response;

    // SUPER_ADMIN인 경우 모든 QR 코드를 가져오는 API 호출
    if (isSuperAdmin) {
      // 현재 선택된 프로젝트 ID가 있으면 파라미터로 전달
      const projectId = currentProject.value?.projectId || null;

      if (projectId) {
        response = await getAllQrCodes(currentPage.value, itemsPerPage.value, projectId);
      } else {
        response = await getAllQrCodes(currentPage.value, itemsPerPage.value);
      }
    } else {
      // 일반 사용자는 프로젝트별 QR 코드 목록 요청
      response = await getQrCodes(currentProject.value.projectId, currentPage.value, itemsPerPage.value);
    }

    // 새로운 API 응답 구조 처리: { success: true, data: { content: [...], totalElements, totalPages, ... } }
    if (response && response.success && response.data) {
      const responseData = response.data;

      // 페이지네이션 정보 업데이트
      if (responseData.totalPages !== undefined) {
        totalPages.value = responseData.totalPages;
      } else {
        // 서버에서 totalPages가 없는 경우 계산
        const total = responseData.totalElements || (responseData.content?.length || 0);
        totalPages.value = Math.ceil(total / itemsPerPage.value) || 1;
      }

      totalElements.value = responseData.totalElements || 0;

      // 검색 유형 목록 업데이트
      if (responseData.availableSearchTypes && Array.isArray(responseData.availableSearchTypes)) {
        availableSearchTypes.value = responseData.availableSearchTypes;

        // 검색 유형이 없는 경우 기본값 설정
        if (availableSearchTypes.value.length > 0 && !searchType.value) {
          searchType.value = availableSearchTypes.value[0].value;
          appliedSearchType.value = availableSearchTypes.value[0].value;
        }
      }

      // QR 코드 데이터 업데이트
      if (responseData.content && Array.isArray(responseData.content)) {
        qrCodes.value = responseData.content;

        // 데이터가 있지만 totalPages가 0인 경우 최소 1로 설정
        if (qrCodes.value.length > 0 && totalPages.value === 0) {
          totalPages.value = 1;
        }
      } else if (Array.isArray(responseData)) {
        // 이전 응답 구조 처리 (호환성 유지)
        qrCodes.value = responseData;
        totalPages.value = 1;
        totalElements.value = responseData.length;
      } else {
        console.warn('QR 코드 데이터가 없거나 배열이 아닙니다.');
        qrCodes.value = [];
      }
    } else {
      console.warn('서버 응답에 데이터가 없습니다.');
      qrCodes.value = [];
      totalPages.value = 0;
      totalElements.value = 0;
    }

  } catch (err) {
    error.value = handleApiError(err, 'QR 코드 목록을 불러오는 중 오류가 발생했습니다.');
    console.error('QR 코드 목록 조회 실패:', err);
    qrCodes.value = [];
    totalPages.value = 0;
    totalElements.value = 0;
  } finally {
    isLoading.value = false;
  }
};

// QR 코드 다운로드 함수
const downloadQrCode = async (qrCode) => {
  if (!qrCode.imageUrl) {
    alert('다운로드할 QR 코드 이미지가 없습니다.');
    return;
  }

  try {
    // 이미지 URL에서 파일명 추출 ('/qrcodes/' 다음에 오는 부분)
    const imageUrl = qrCode.imageUrl;
    let fileName = '';

    // '/qrcodes/' 다음에 오는 부분을 파일명으로 추출
    const qrcodesIndex = imageUrl.indexOf('/qrcodes/');
    if (qrcodesIndex !== -1) {
      fileName = imageUrl.substring(qrcodesIndex + '/qrcodes/'.length);
    } else {
      // 기존 방식으로 폴백: 마지막 '/' 이후의 문자열을 파일명으로 사용
      fileName = imageUrl.split('/').pop();
    }

    if (!fileName) {
      alert('파일명을 추출할 수 없습니다.');
      return;
    }

    // 파일 다운로드 경로
    const downloadPath = `/download/${fileName}`;

    // apiClient를 사용하여 파일 다운로드 요청
    // apiClient는 이미 기본 URL과 인증 헤더가 설정되어 있음
    const response = await apiClient.get(downloadPath, {
      responseType: 'blob' // 중요: 바이너리 데이터로 받기 위해 필요
    });

    // 다운로드 링크 생성 및 클릭
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;

    // 다운로드 파일명 설정 (QR 코드 이름과 원본 파일 확장자 사용)
    // 원본 파일명에서 확장자 추출
    let fileExtension = fileName.split('.').pop() || 'svg';

    // Content-Type 헤더에서 확장자 확인 (더 정확함)
    const contentType = response.headers['content-type'];
    if (contentType) {
      if (contentType.includes('image/svg+xml')) {
        fileExtension = 'svg';
      } else if (contentType.includes('image/png')) {
        fileExtension = 'png';
      } else if (contentType.includes('image/jpeg')) {
        fileExtension = 'jpg';
      } else if (contentType.includes('image/gif')) {
        fileExtension = 'gif';
      }
    }

    const downloadFileName = `${qrCode.qrName || 'qrcode'}.${fileExtension}`;
    link.setAttribute('download', downloadFileName);

    document.body.appendChild(link);
    link.click();

    // 링크 제거
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);

  } catch (error) {
    console.error('QR 코드 다운로드 실패:', error);

    // 공통 함수를 사용하여 오류 메시지 추출
    const errorMessage = extractErrorMessage(error);
    alert(errorMessage);
  }
};

// QR 코드 선택 토글
const toggleSelectQrCode = (qrCodeId) => {
  const index = selectedQrCodeIds.value.indexOf(qrCodeId);
  if (index === -1) {
    // 선택되지 않은 경우 추가
    selectedQrCodeIds.value.push(qrCodeId);
  } else {
    // 이미 선택된 경우 제거
    selectedQrCodeIds.value.splice(index, 1);
  }
};

// 전체 선택/해제 토글
const toggleSelectAll = (event) => {
  if (event.target.checked) {
    // 전체 선택 (이미지 URL이 있는 QR 코드만)
    selectedQrCodeIds.value = filteredQrCodes.value
      .filter(qrCode => qrCode.imageUrl)
      .map(qrCode => qrCode.qrCodeId);
  } else {
    // 전체 해제
    selectedQrCodeIds.value = [];
  }
};

// 선택한 QR 코드 삭제 요청 처리
const bulkDeleteQrCodes = async () => {
  if (selectedQrCodeIds.value.length === 0) {
    return;
  }

  if (!confirm(`선택한 ${selectedQrCodeIds.value.length}개의 QR 코드를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.`)) {
    return;
  }

  isMultipleDeleting.value = true;

  try {
    // API 호출 - deleteMultipleQrCodes 함수 사용
    const result = await deleteMultipleQrCodes(selectedQrCodeIds.value);
    
    if (result) {
      // 성공 메시지 표시
      alert(`${selectedQrCodeIds.value.length}개의 QR 코드가 성공적으로 삭제되었습니다.`);
      
      // 선택 초기화
      selectedQrCodeIds.value = [];
      
      // 목록 새로고침
      await fetchQrCodes();
    }
  } catch (err) {
    // 에러 메시지 추출 및 표시
    const errorMessage = extractErrorMessage(err);
    error.value = errorMessage;
    alert(`QR 코드 삭제 중 오류가 발생했습니다: ${errorMessage}`);
    console.error('선택한 QR 코드 삭제 실패:', err);
  } finally {
    isMultipleDeleting.value = false;
  }
};

// 선택한 QR 코드 일괄 다운로드
const bulkDownloadQrCodes = async () => {
  if (selectedQrCodeIds.value.length === 0) {
    alert('선택된 QR 코드가 없습니다.');
    return;
  }

  isDownloading.value = true;

  try {
    // 선택된 QR 코드 목록 가져오기
    const selectedQrCodes = qrCodes.value.filter(qrCode =>
      selectedQrCodeIds.value.includes(qrCode.qrCodeId) && qrCode.imageUrl
    );

    if (selectedQrCodes.length === 0) {
      alert('다운로드할 QR 코드 이미지가 없습니다.');
      return;
    }

    // 다운로드 진행 상태 추적
    let currentIndex = 0;
    let currentQrCode = null;

    try {
      // 모든 QR 코드를 순차적으로 다운로드
      for (currentIndex = 0; currentIndex < selectedQrCodes.length; currentIndex++) {
        currentQrCode = selectedQrCodes[currentIndex];

        // 이미지 URL에서 파일명 추출 ('/qrcodes/' 다음에 오는 부분)
        const imageUrl = currentQrCode.imageUrl;
        let fileName = '';

        // '/qrcodes/' 다음에 오는 부분을 파일명으로 추출
        const qrcodesIndex = imageUrl.indexOf('/qrcodes/');
        if (qrcodesIndex !== -1) {
          fileName = imageUrl.substring(qrcodesIndex + '/qrcodes/'.length);
        } else {
          // 기존 방식으로 폴백: 마지막 '/' 이후의 문자열을 파일명으로 사용
          fileName = imageUrl.split('/').pop();
        }

        if (!fileName) {
          throw new Error(`파일명을 추출할 수 없습니다: ${imageUrl}`);
        }

        // 파일 다운로드 경로
        const downloadPath = `/download/${fileName}`;

        // apiClient를 사용하여 파일 다운로드 요청
        // apiClient는 이미 기본 URL과 인증 헤더가 설정되어 있음
        const response = await apiClient.get(downloadPath, {
          responseType: 'blob' // 중요: 바이너리 데이터로 받기 위해 필요
        });

        // 다운로드 링크 생성 및 클릭
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;

        // 다운로드 파일명 설정 (QR 코드 이름과 원본 파일 확장자 사용)
        // 원본 파일명에서 확장자 추출
        let fileExtension = fileName.split('.').pop() || 'svg';

        // Content-Type 헤더에서 확장자 확인 (더 정확함)
        const contentType = response.headers['content-type'];
        if (contentType) {
          if (contentType.includes('image/svg+xml')) {
            fileExtension = 'svg';
          } else if (contentType.includes('image/png')) {
            fileExtension = 'png';
          } else if (contentType.includes('image/jpeg')) {
            fileExtension = 'jpg';
          } else if (contentType.includes('image/gif')) {
            fileExtension = 'gif';
          }
        }

        const downloadFileName = `${currentQrCode.qrName || 'qrcode'}.${fileExtension}`;
        link.setAttribute('download', downloadFileName);

        document.body.appendChild(link);
        link.click();

        // 링크 제거
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);

        // 다운로드 간 약간의 지연 추가
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // 모든 다운로드 성공
      alert(`${selectedQrCodes.length}개의 QR 코드 다운로드가 완료되었습니다.`);

    } catch (error) {
      // 현재 처리 중이던 QR 코드 정보 사용
      const failedQrName = currentQrCode?.qrName || '알 수 없는 QR 코드';
      const failedQrId = currentQrCode?.qrCodeId || '';

      // 실패한 QR 코드 정보 로깅
      console.error(`QR 코드 다운로드 실패 (${failedQrName}${failedQrId ? `, ID: ${failedQrId}` : ''}, 인덱스: ${currentIndex+1}/${selectedQrCodes.length}):`, error);

      // 오류 메시지 추출
      const errorMessage = error.response
        ? extractErrorMessage(error)
        : error.message || '알 수 없는 오류';

      // 사용자에게 오류 알림 (실패한 QR 코드 정보 포함)
      alert(`"${failedQrName}" QR 코드 다운로드 중 오류가 발생하여 다운로드가 중단되었습니다.\n(${currentIndex+1}번째 항목 / 전체 ${selectedQrCodes.length}개)\n\n오류: ${errorMessage}`);
    }

    // 이 부분은 실행되지 않습니다. 위의 try-catch 블록에서 모든 처리가 완료됩니다.
  } catch (error) {
    console.error('QR 코드 일괄 다운로드 중 예상치 못한 오류:', error);

    // 공통 함수를 사용하여 오류 메시지 추출
    const errorMessage = extractErrorMessage(error);

    // 어떤 단계에서 실패했는지 확인
    let stageMessage = '초기화 단계';

    if (error.message && error.message.includes('선택된 QR 코드')) {
      stageMessage = 'QR 코드 선택 단계';
    } else if (error.message && error.message.includes('다운로드')) {
      stageMessage = 'QR 코드 다운로드 단계';
    }

    alert(`일괄 다운로드 중 오류가 발생했습니다 (${stageMessage}):\n${errorMessage}`);
  } finally {
    isDownloading.value = false;
  }
};

// 프로젝트 변경 이벤트 핸들러
const handleProjectChange = () => {
  // 프로젝트 변경 시 검색 초기화
  resetSearch();
};

// 컴포넌트 마운트 시 QR 코드 목록 로드 및 이벤트 리스너 등록
onMounted(() => {
  // SUPER_ADMIN인 경우 프로젝트 ID 없이도 QR 코드 목록을 로드
  const isSuperAdmin = authStore.user?.roleId === 'SUPER_ADMIN';

  if (isSuperAdmin || currentProject.value?.projectId) {
    fetchQrCodes();
  }

  // 프로젝트 변경 이벤트 리스너 등록
  window.addEventListener('project-changed', handleProjectChange);
});

// 컴포넌트 언마운트 시 이벤트 리스너 제거
onUnmounted(() => {
  window.removeEventListener('project-changed', handleProjectChange);
});

// 서버 오류 메시지 추출 함수
const extractErrorMessage = (error) => {
  let errorMessage = 'QR 코드 다운로드 중 오류가 발생했습니다. 다시 시도해주세요.';

  if (error.response) {
    // 서버 응답이 있는 경우
    if (error.response.data) {
      if (error.response.data.error && error.response.data.error.message) {
        // 표준 API 응답 형식 (error.message)
        errorMessage = error.response.data.error.message;
      } else if (typeof error.response.data === 'string') {
        // 텍스트 형식 응답
        errorMessage = error.response.data;
      } else if (error.response.data.message) {
        // 다른 형식의 응답 (message 필드)
        errorMessage = error.response.data.message;
      }
    }
  } else if (error.message) {
    // 네트워크 오류 등 서버 응답이 없는 경우
    errorMessage = error.message;
  }

  return errorMessage;
};

// 검색어 적용 또는 정렬 변경 시 선택 초기화
watch([appliedSearchQuery, appliedSearchType, sortKey, sortOrder], () => {
  selectedQrCodeIds.value = [];
});
</script>

<style scoped>
.qr-management {
  padding: 20px;
}

.qr-list-container {
  margin-top: 20px;
}

/* 필터 스타일 */
.filters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 500;
  color: #555;
}

.filter-group input, .filter-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 200px;
}

.create-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.create-btn:hover {
  background-color: #45a049;
}

.search-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.search-btn:hover {
  background-color: #0b7dda;
}

.reset-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.reset-btn:hover {
  background-color: #d32f2f;
}

.qr-table-container {
  overflow-x: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.qr-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0;
}

.qr-table th {
  background-color: #f2f2f2;
  padding: 12px 8px;
  text-align: left;
  border-bottom: 2px solid #ddd;
  cursor: pointer;
  user-select: none;
}

.qr-table th:hover {
  background-color: #e6e6e6;
}

.qr-table td {
  padding: 10px 8px;
  border-bottom: 1px solid #ddd;
}

.qr-table tr:hover {
  background-color: #f5f5f5;
}

.target-content {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  min-width: 120px;
}

/* 액션 버튼 스타일 */
.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  margin-right: 6px;
  transition: background-color 0.2s;
}

.view-btn {
  background-color: #e3f2fd;
  color: #1976d2;
}

.view-btn:hover {
  background-color: #bbdefb;
}

.edit-btn {
  background-color: #fff8e1;
  color: #ffa000;
}

.edit-btn:hover {
  background-color: #ffecb3;
}

.delete-btn {
  background-color: #ffebee;
  color: #d32f2f;
}

.delete-btn:hover {
  background-color: #ffcdd2;
}

.download-btn {
  background-color: #e8f5e9;
  color: #388e3c;
}

.download-btn:hover {
  background-color: #c8e6c9;
}

/* 체크박스 열 스타일 */
.checkbox-column {
  width: 40px;
  text-align: center;
}

.checkbox-column input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

/* 일괄 작업 영역 스타일 */
.bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f0f7ff;
  border-radius: 8px 8px 0 0;
  margin-bottom: 0;
  border-bottom: 1px solid #d0e3ff;
}

.selected-count {
  font-weight: 500;
  color: #1976d2;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.bulk-action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.bulk-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 로딩 및 에러 메시지 스타일 */
.loading-message,
.no-data-message,
.no-projects-message {
  padding: 40px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  padding: 20px;
  text-align: center;
  background-color: #ffebee;
  color: #d32f2f;
  border-radius: 4px;
  margin: 20px 0;
}

/* 페이지네이션 스타일 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  border-color: #aaa;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  padding: 0 12px;
  color: #555;
}

/* 디버깅 정보 스타일 */
/* QR 코드 미리보기 스타일 */
.qr-preview-cell {
  position: relative;
}

.qr-preview-container {
  position: relative;
  display: inline-block;
}

.qr-preview-thumbnail {
  width: 40px;
  height: 40px;
  object-fit: contain;
  cursor: pointer;
}

.qr-preview-modal {
  display: none;
  position: absolute;
  z-index: 100;
  padding: 10px;
  background-color: white;
  border: 1px solid #ddd;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  left: 50%;
  transform: translateX(-50%);
  top: 100%;
}

.qr-preview-large {
  width: 200px;
  height: 200px;
  object-fit: contain;
}

.qr-preview-container:hover .qr-preview-modal {
  display: block;
}

.debug-info {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  color: #666;
}

.debug-info p {
  margin: 5px 0;
}

/* 모달 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
  margin-top: 0;
  color: #333;
}

.warning {
  color: #f44336;
  font-weight: bold;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.confirm-btn, .cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-btn {
  background-color: #F44336;
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background-color: #da190b;
}

.confirm-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.cancel-btn {
  background-color: #e0e0e0;
  color: #333;
}

.cancel-btn:hover {
  background-color: #d0d0d0;
}
/* 페이지당 항목 수 선택 UI 스타일 */
.items-per-page-selector {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.items-per-page-selector span {
  margin-right: 10px;
  font-size: 0.9rem;
  color: #555;
}

.items-per-page-buttons {
  display: flex;
}

.item-count-btn {
  padding: 5px 10px;
  margin: 0 3px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.item-count-btn:hover {
  background-color: #e0e0e0;
}

.item-count-btn.active {
  background-color: #4CAF50;
  color: white;
  border-color: #4CAF50;
}
</style>
