import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { v4 as uuidv4 } from 'uuid';

export const useLandingEditorStore = defineStore('landingEditor', () => {
  // 상태 (State)
  const currentLandingPage = ref(null);
  const selectedElementId = ref(null);
  const history = ref({
    past: [],
    future: []
  });
  const isEditing = ref(false);
  const isSaving = ref(false);
  const lastSaved = ref(null);
  const errors = ref([]);

  // 작업 그룹화를 위한 상태
  const isGroupingOperations = ref(false);
  const hasUnsavedChanges = ref(false);

  // 캔버스 기본 설정 (모바일 화면에 맞게 설정)
  const defaultCanvas = {
    width: 375, // 모바일 화면 너비
    height: 667, // 모바일 화면 높이 (iPhone 8 기준)
    backgroundColor: '#ffffff',
    backgroundImage: null,
    backgroundFit: 'cover' // 배경 이미지 맞춤 방식 (기본값: cover)
  };

  // 랜딩 페이지 기본 설정
  const defaultSettings = {
    theme: {
      primaryColor: '#3498db',
      secondaryColor: '#2ecc71',
      fontFamily: 'Arial, sans-serif'
    },
    meta: {
      title: '새 랜딩 페이지',
      description: '',
      keywords: ''
    }
  };

  // 게터 (Getters)
  const selectedElement = computed(() => {
    if (!currentLandingPage.value || !selectedElementId.value) return null;
    return currentLandingPage.value.elements.find(el => el.id === selectedElementId.value);
  });

  const canUndo = computed(() => history.value.past.length > 0);
  const canRedo = computed(() => history.value.future.length > 0);

  // 액션 (Actions)
  // 새 랜딩 페이지 생성
  function createNewLandingPage(projectId, landingPageName = '새 랜딩 페이지', template = null) {
    // 기본 랜딩 페이지 구조 생성
    currentLandingPage.value = {
      landingPageId: null, // 서버에서 할당될 ID
      projectId,
      landingPageName,
      description: '',
      status: 'ACTIVE',
      url: '',
      visitCount: 0,
      createDate: new Date().toISOString(),
      validFromDate: null,
      validToDate: null,
      canvas: { ...defaultCanvas },
      elements: [],
      settings: JSON.parse(JSON.stringify(defaultSettings))
    };

    // 템플릿이 있는 경우 템플릿 적용
    if (template) {
      applyTemplate(template);
    }

    // 히스토리 초기화
    history.value = { past: [], future: [] };
    selectedElementId.value = null;
    isEditing.value = true;
    errors.value = [];

    return currentLandingPage.value;
  }

  // 템플릿 적용
  function applyTemplate(template) {
    if (!currentLandingPage.value || !template) return;

    // 히스토리에 현재 상태 저장
    saveStateToHistory();

    // 캔버스 설정 적용
    if (template.canvas) {
      currentLandingPage.value.canvas = { ...template.canvas };
    }

    // 요소 적용 (고유 ID 생성)
    if (template.elements && Array.isArray(template.elements)) {
      // 깃은 복사를 통해 중첩된 객체도 정확히 복사
      const deepCopiedElements = JSON.parse(JSON.stringify(template.elements));

      // 각 요소에 새로운 ID 할당
      currentLandingPage.value.elements = deepCopiedElements.map(element => ({
        ...element,
        id: uuidv4() // 새로운 고유 ID 생성
      }));
    }

    // 설정 적용 (선택적)
    if (template.settings) {
      currentLandingPage.value.settings = { ...template.settings };
    }

    return currentLandingPage.value;
  }

  // 랜딩 페이지 불러오기
  function loadLandingPage(landingPageData) {
    currentLandingPage.value = landingPageData;
    history.value = { past: [], future: [] };
    selectedElementId.value = null;
    isEditing.value = true;
    errors.value = [];

    return currentLandingPage.value;
  }

  // 상태 스냅샷 저장 (히스토리 관리용)
  function saveStateToHistory() {
    if (!currentLandingPage.value) return;

    // 작업 그룹화 중이면 상태를 저장하지 않고 변경 사항이 있음을 표시
    if (isGroupingOperations.value) {
      hasUnsavedChanges.value = true;
      return;
    }

    // 현재 상태의 깊은 복사본 생성
    const currentState = JSON.parse(JSON.stringify(currentLandingPage.value));

    // 과거 상태에 추가
    history.value.past.push(currentState);

    // 미래 상태 초기화 (새 변경사항이 있으면 미래 히스토리는 무효화)
    history.value.future = [];

    // 히스토리 크기 제한 (선택적)
    // if (history.value.past.length > 30) {
    //   history.value.past.shift(); // 가장 오래된 상태 제거
    // }
  }

  // 작업 그룹화 시작
  function startGroupingOperations() {
    // 작업 그룹화 전에 현재 상태 저장
    if (!isGroupingOperations.value) {
      saveStateToHistory();
    }

    isGroupingOperations.value = true;
    hasUnsavedChanges.value = false;
  }

  // 작업 그룹화 종료
  function endGroupingOperations() {
    // 작업 그룹화 중에 변경 사항이 있었으면 상태 저장
    if (isGroupingOperations.value && hasUnsavedChanges.value) {
      isGroupingOperations.value = false;
      hasUnsavedChanges.value = false;
      saveStateToHistory();
    } else {
      isGroupingOperations.value = false;
      hasUnsavedChanges.value = false;
    }
  }

  // 실행 취소
  function undo() {
    if (!canUndo.value) return;

    // 현재 선택된 요소 ID 저장
    const currentSelectedId = selectedElementId.value;

    // 현재 상태를 미래 히스토리에 저장
    const currentState = JSON.parse(JSON.stringify(currentLandingPage.value));
    history.value.future.unshift(currentState);

    // 과거 상태에서 가장 최근 상태를 가져옴
    const previousState = history.value.past.pop();
    currentLandingPage.value = previousState;

    // 선택된 요소 ID 유지
    // 만약 이전에 선택된 요소가 있고, 해당 요소가 여전히 존재하면 선택 상태 유지
    if (currentSelectedId) {
      const elementExists = currentLandingPage.value.elements.some(el => el.id === currentSelectedId);
      if (elementExists) {
        selectedElementId.value = currentSelectedId;
      } else {
        // 요소가 존재하지 않으면 선택 해제
        selectedElementId.value = null;
      }
    }
  }

  // 다시 실행
  function redo() {
    if (!canRedo.value) return;

    // 현재 선택된 요소 ID 저장
    const currentSelectedId = selectedElementId.value;

    // 미래 상태에서 가장 최근 상태를 가져옴
    const nextState = history.value.future.shift();

    // 현재 상태를 과거 히스토리에 저장
    const currentState = JSON.parse(JSON.stringify(currentLandingPage.value));
    history.value.past.push(currentState);

    // 미래 상태를 현재 상태로 설정
    currentLandingPage.value = nextState;

    // 선택된 요소 ID 유지
    // 만약 이전에 선택된 요소가 있고, 해당 요소가 여전히 존재하면 선택 상태 유지
    if (currentSelectedId) {
      const elementExists = currentLandingPage.value.elements.some(el => el.id === currentSelectedId);
      if (elementExists) {
        selectedElementId.value = currentSelectedId;
      } else {
        // 요소가 존재하지 않으면 선택 해제
        selectedElementId.value = null;
      }
    }
  }

  // 요소 추가
  function addElement(elementType, defaultProps = {}) {
    if (!currentLandingPage.value) return null;

    // 히스토리에 현재 상태 저장
    saveStateToHistory();

    // 요소 타입별 기본 콘텐츠 정의
    const defaultContents = {
      text: {
        text: '텍스트를 입력하세요',
        fontSize: 16,
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'normal',
        fontStyle: 'normal',
        textAlign: 'left',
        color: '#000000',
        lineHeight: 1.5,
        letterSpacing: 'normal'
      },
      image: {
        src: '',
        alt: '이미지',
        objectFit: 'contain',
        filter: null
      },
      button: {
        text: '버튼',
        textStyle: {
          fontSize: 16,
          fontFamily: 'Arial, sans-serif',
          fontWeight: 'bold',
          color: '#ffffff'
        },
        action: {
          type: 'link',
          target: '',
          newTab: false
        },
        hoverStyle: {
          backgroundColor: '#2980b9',
          color: '#ffffff'
        },
        icon: {
          src: null,
          position: 'left'
        }
      },
      link: {
        text: '링크',
        url: '',
        newTab: false,
        textStyle: {
          fontSize: 16,
          fontFamily: 'Arial, sans-serif',
          fontWeight: 'normal',
          color: '#3498db',
          textDecoration: 'underline'
        },
        hoverStyle: {
          color: '#2980b9',
          textDecoration: 'underline'
        }
      },
      div: {
        overflow: 'visible'
      }
    };

    // 기본 스타일 정의
    const defaultStyle = {
      opacity: 1,
      rotation: 0,
      backgroundColor: elementType === 'button' ? '#3498db' : null,
      borderWidth: 0,
      borderColor: null,
      borderStyle: null,
      borderRadius: elementType === 'button' ? 4 : 0,
      padding: elementType === 'button' ? '8px 16px' : '0',
      margin: '0',
      boxShadow: null
    };

    // 새 요소 생성
    const newElement = {
      id: uuidv4(), // 고유 ID 생성
      type: elementType,
      name: `${elementType}-${currentLandingPage.value.elements.length + 1}`,
      position: {
        x: { value: 0, unit: 'px' },
        y: { value: 0, unit: 'px' }
      },
      size: {
        width: elementType === 'text' || elementType === 'link' ?
          { value: 'auto', unit: 'auto' } :
          { value: 200, unit: 'px' },
        height: elementType === 'text' || elementType === 'link' ?
          { value: 'auto', unit: 'auto' } :
          { value: 100, unit: 'px' }
      },
      style: { ...defaultStyle },
      content: { ...defaultContents[elementType] },
      visible: true,
      locked: false,
      zIndex: currentLandingPage.value.elements.length + 1,
      ...defaultProps
    };

    // 요소 목록에 추가
    currentLandingPage.value.elements.push(newElement);

    // 새 요소 선택
    selectedElementId.value = newElement.id;

    return newElement;
  }

  // 요소 선택
  function selectElement(elementId) {
    selectedElementId.value = elementId;
  }

  // 요소 삭제
  function deleteElement(elementId) {
    if (!currentLandingPage.value) return;

    const elementToDelete = currentLandingPage.value.elements.find(el => el.id === elementId);
    if (!elementToDelete) return;

    // 히스토리에 현재 상태 저장
    saveStateToHistory();

    // 요소 삭제
    currentLandingPage.value.elements = currentLandingPage.value.elements.filter(el => el.id !== elementId);

    // 선택된 요소가 삭제된 요소인 경우 선택 해제
    if (selectedElementId.value === elementId) {
      selectedElementId.value = null;
    }
  }

  // 요소 가시성 토글
  function toggleElementVisibility(elementId) {
    if (!currentLandingPage.value) return;

    const elementIndex = currentLandingPage.value.elements.findIndex(el => el.id === elementId);
    if (elementIndex === -1) return;

    // 히스토리에 현재 상태 저장
    saveStateToHistory();

    // 가시성 토글
    const currentVisibility = currentLandingPage.value.elements[elementIndex].visible;
    currentLandingPage.value.elements[elementIndex].visible = !currentVisibility;
  }

  // 요소 이름 변경
  function renameElement(elementId, newName) {
    if (!currentLandingPage.value) return;

    const elementIndex = currentLandingPage.value.elements.findIndex(el => el.id === elementId);
    if (elementIndex === -1) return;

    // 히스토리에 현재 상태 저장
    saveStateToHistory();

    // 이름 변경
    currentLandingPage.value.elements[elementIndex].name = newName;
  }

  // 요소 속성 업데이트
  function updateElementProperty(elementId, propertyPath, value) {
    if (!currentLandingPage.value) return;

    const elementIndex = currentLandingPage.value.elements.findIndex(el => el.id === elementId);
    if (elementIndex === -1) return;

    // 히스토리에 현재 상태 저장 (성능 최적화를 위해 디바운스 고려)
    saveStateToHistory();

    // 속성 경로 파싱 (예: 'content.text', 'style.backgroundColor')
    const pathParts = propertyPath.split('.');
    let current = currentLandingPage.value.elements[elementIndex];

    // 마지막 속성 이전까지 탐색
    for (let i = 0; i < pathParts.length - 1; i++) {
      current = current[pathParts[i]];
      if (current === undefined) return; // 잘못된 경로
    }

    // 마지막 속성 업데이트
    current[pathParts[pathParts.length - 1]] = value;
  }

  // 요소 위치 업데이트
  function updateElementPosition(elementId, x, y) {
    try {
      if (!currentLandingPage.value) {
        console.warn('현재 랜딩 페이지가 없습니다.');
        return;
      }

      const elementIndex = currentLandingPage.value.elements.findIndex(el => el.id === elementId);
      if (elementIndex === -1) {
        console.warn('요소를 찾을 수 없습니다:', elementId);
        return;
      }

      // 히스토리에 현재 상태 저장 (작업 그룹화 중이 아닐 때만)
      // 작업 그룹화 중일 때는 상태를 저장하지 않음
      if (!isGroupingOperations.value) {
        saveStateToHistory();
      }

      const element = currentLandingPage.value.elements[elementIndex];

      // position 객체 구조 확인 및 초기화
      if (!element.position) {
        element.position = { x: { value: 0, unit: 'px' }, y: { value: 0, unit: 'px' } };
      }

      // x 위치 객체 구조 확인 및 초기화
      if (!element.position.x || typeof element.position.x !== 'object') {
        const xValue = element.position.x || 0;
        element.position.x = { value: xValue, unit: 'px' };
      }

      // y 위치 객체 구조 확인 및 초기화
      if (!element.position.y || typeof element.position.y !== 'object') {
        const yValue = element.position.y || 0;
        element.position.y = { value: yValue, unit: 'px' };
      }

      // 현재 위치 가져오기
      let currentX = 0;
      let currentY = 0;

      if (element.position.x && typeof element.position.x === 'object') {
        currentX = Number(element.position.x.value) || 0;
      } else {
        currentX = Number(element.position.x) || 0;
      }

      if (element.position.y && typeof element.position.y === 'object') {
        currentY = Number(element.position.y.value) || 0;
      } else {
        currentY = Number(element.position.y) || 0;
      }

      // 기본값 근처인지 확인하는 함수
      const isNearDefaultValue = (value) => {
        // 95px ~ 105px 범위를 100px 근처로 간주
        if (value >= 95 && value <= 105) return true;

        // 45px ~ 55px 범위를 50px 근처로 간주
        if (value >= 45 && value <= 55) return true;

        return false;
      };

      if (typeof x === 'object' && x !== null) {
        // x가 {value, unit} 형태로 전달된 경우
        const xValue = Number(x.value);

        // 값이 NaN이거나 기본값 근처로 고정되는 문제 방지
        if (!isNaN(xValue)) {
          // 드래그 중에는 기본값 근처 감지 로직을 적용하지 않음
          // 이를 통해 드래그 중 위치가 기본값 근처로 설정되는 문제 방지

          // 기본값 근처 감지
          if (isNearDefaultValue(xValue)) {
            console.warn('스토어 - x 위치가 기본값 근처(' + xValue + 'px)입니다.');
          }

          // 위치 업데이트
          const oldValue = element.position.x.value;
          element.position.x = {
            value: xValue,
            unit: x.unit || 'px'
          };
        } else {
          console.warn('스토어 - 잘못된 x 값:', x.value);
        }
      } else if (x !== undefined && x !== null) {
        // x가 숫자나 문자열로 전달된 경우 (기존 단위 유지)
        const xValue = Number(x);

        if (!isNaN(xValue)) {
          // 드래그 중에는 기본값 근처 감지 로직을 적용하지 않음
          // 이를 통해 드래그 중 위치가 기본값 근처로 설정되는 문제 방지

          // 기본값 근처 감지
          if (isNearDefaultValue(xValue)) {
            console.warn('스토어 - x 위치가 기본값 근처(' + xValue + 'px)입니다.');
          }

          // 위치 업데이트
          const oldValue = element.position.x.value;
          element.position.x.value = xValue;
        } else {
          console.warn('스토어 - 잘못된 x 값:', x);
        }
      }


      if (typeof y === 'object' && y !== null) {
        // y가 {value, unit} 형태로 전달된 경우
        const yValue = Number(y.value);

        // 값이 NaN이거나 기본값 근처로 고정되는 문제 방지
        if (!isNaN(yValue)) {
          // 드래그 중에는 기본값 근처 감지 로직을 적용하지 않음
          // 이를 통해 드래그 중 위치가 기본값 근처로 설정되는 문제 방지

          // 기본값 근처 감지
          if (isNearDefaultValue(yValue)) {
            console.warn('스토어 - y 위치가 기본값 근처(' + yValue + 'px)입니다.');
          }

          // 위치 업데이트
          const oldValue = element.position.y.value;
          element.position.y = {
            value: yValue,
            unit: y.unit || 'px'
          };
        } else {
          console.warn('스토어 - 잘못된 y 값:', y.value);
        }
      } else if (y !== undefined && y !== null) {
        // y가 숫자나 문자열로 전달된 경우 (기존 단위 유지)
        const yValue = Number(y);

        if (!isNaN(yValue)) {
          // 드래그 중에는 기본값 근처 감지 로직을 적용하지 않음
          // 이를 통해 드래그 중 위치가 기본값 근처로 설정되는 문제 방지

          // 기본값 근처 감지
          if (isNearDefaultValue(yValue)) {
            console.warn('스토어 - y 위치가 기본값 근처(' + yValue + 'px)입니다.');
          }

          // 위치 업데이트
          const oldValue = element.position.y.value;
          element.position.y.value = yValue;
        } else {
          console.warn('스토어 - 잘못된 y 값:', y);
        }
      }

    } catch (error) {
      console.error('스토어 - 위치 업데이트 오류:', error);
    }
  }

  // 요소 크기 업데이트
  function updateElementSize(elementId, width, height) {
    if (!currentLandingPage.value) return;

    const elementIndex = currentLandingPage.value.elements.findIndex(el => el.id === elementId);
    if (elementIndex === -1) return;

    // 히스토리에 현재 상태 저장 (작업 그룹화 중이 아닐 때만)
    // 작업 그룹화 중일 때는 상태를 저장하지 않음
    if (!isGroupingOperations.value) {
      saveStateToHistory();
    }

    const element = currentLandingPage.value.elements[elementIndex];

    // 크기 업데이트 (단위 유지)
    if (typeof width === 'object' && width !== null) {
      // width가 {value, unit} 형태로 전달된 경우
      element.size.width = { ...width };
    } else if (width === 'auto') {
      // 'auto' 값인 경우
      element.size.width = { value: 'auto', unit: 'auto' };
    } else {
      // 숫자나 다른 값인 경우 (기존 단위 유지)
      element.size.width.value = width;
    }

    if (typeof height === 'object' && height !== null) {
      // height가 {value, unit} 형태로 전달된 경우
      element.size.height = { ...height };
    } else if (height === 'auto') {
      // 'auto' 값인 경우
      element.size.height = { value: 'auto', unit: 'auto' };
    } else {
      // 숫자나 다른 값인 경우 (기존 단위 유지)
      element.size.height.value = height;
    }
  }

  // 캔버스 속성 업데이트
  function updateCanvasProperty(propertyPath, value) {
    if (!currentLandingPage.value) return;

    // 히스토리에 현재 상태 저장
    saveStateToHistory();

    // 속성 경로 파싱 (예: 'backgroundColor', 'width')
    const pathParts = propertyPath.split('.');
    let current = currentLandingPage.value.canvas;

    // 마지막 속성 이전까지 탐색
    for (let i = 0; i < pathParts.length - 1; i++) {
      current = current[pathParts[i]];
      if (current === undefined) return; // 잘못된 경로
    }

    // 마지막 속성 업데이트
    current[pathParts[pathParts.length - 1]] = value;
  }

  // 랜딩 페이지 설정 업데이트
  function updatePageSettings(propertyPath, value) {
    if (!currentLandingPage.value) return;

    // 히스토리에 현재 상태 저장
    saveStateToHistory();

    // 속성 경로 파싱 (예: 'theme.primaryColor', 'meta.title')
    const pathParts = propertyPath.split('.');
    let current = currentLandingPage.value.settings;

    // 마지막 속성 이전까지 탐색
    for (let i = 0; i < pathParts.length - 1; i++) {
      current = current[pathParts[i]];
      if (current === undefined) return; // 잘못된 경로
    }

    // 마지막 속성 업데이트
    current[pathParts[pathParts.length - 1]] = value;
  }

  // 요소 Z-인덱스 변경 (앞으로 가져오기)
  function bringElementForward(elementId) {
    if (!currentLandingPage.value) return;

    const elementIndex = currentLandingPage.value.elements.findIndex(el => el.id === elementId);
    if (elementIndex === -1) return;

    const currentZIndex = currentLandingPage.value.elements[elementIndex].zIndex;
    const nextElement = currentLandingPage.value.elements.find(el => el.zIndex === currentZIndex + 1);

    if (nextElement) {
      // 히스토리에 현재 상태 저장
      saveStateToHistory();

      // Z-인덱스 교환
      nextElement.zIndex = currentZIndex;
      currentLandingPage.value.elements[elementIndex].zIndex = currentZIndex + 1;
    }
  }

  // 요소 Z-인덱스 변경 (뒤로 보내기)
  function sendElementBackward(elementId) {
    if (!currentLandingPage.value) return;

    const elementIndex = currentLandingPage.value.elements.findIndex(el => el.id === elementId);
    if (elementIndex === -1) return;

    const currentZIndex = currentLandingPage.value.elements[elementIndex].zIndex;
    const prevElement = currentLandingPage.value.elements.find(el => el.zIndex === currentZIndex - 1);

    if (prevElement) {
      // 히스토리에 현재 상태 저장
      saveStateToHistory();

      // Z-인덱스 교환
      prevElement.zIndex = currentZIndex;
      currentLandingPage.value.elements[elementIndex].zIndex = currentZIndex - 1;
    }
  }

  // 요소 Z-인덱스 변경 (맨 앞으로)
  function bringElementToFront(elementId) {
    if (!currentLandingPage.value) return;

    const elementIndex = currentLandingPage.value.elements.findIndex(el => el.id === elementId);
    if (elementIndex === -1) return;

    // 히스토리에 현재 상태 저장
    saveStateToHistory();

    // 최대 Z-인덱스 찾기
    const maxZIndex = Math.max(...currentLandingPage.value.elements.map(el => el.zIndex));

    // 선택된 요소의 Z-인덱스를 최대값 + 1로 설정
    currentLandingPage.value.elements[elementIndex].zIndex = maxZIndex + 1;
  }

  // 요소 Z-인덱스 변경 (맨 뒤로)
  function sendElementToBack(elementId) {
    if (!currentLandingPage.value) return;

    const elementIndex = currentLandingPage.value.elements.findIndex(el => el.id === elementId);
    if (elementIndex === -1) return;

    // 히스토리에 현재 상태 저장
    saveStateToHistory();

    // 최소 Z-인덱스 찾기
    const minZIndex = Math.min(...currentLandingPage.value.elements.map(el => el.zIndex));

    // 선택된 요소의 Z-인덱스를 최소값 - 1로 설정
    currentLandingPage.value.elements[elementIndex].zIndex = minZIndex - 1;
  }

  // 캔버스 크기 조정
  function resizeCanvas(width, height) {
    if (!currentLandingPage.value) return;

    // 히스토리에 현재 상태 저장
    saveStateToHistory();

    // 너비 유효성 검사 (375px ~ 500px)
    const validWidth = Math.max(375, Math.min(500, width));

    // 높이 유효성 검사 (500px ~ 5000px)
    const validHeight = Math.max(500, Math.min(5000, height));

    // 캔버스 크기 업데이트
    currentLandingPage.value.canvas.width = validWidth;
    currentLandingPage.value.canvas.height = validHeight;
  }

  // 오류 추가
  function addError(errorMessage) {
    errors.value.push(errorMessage);
  }

  // 오류 초기화
  function clearErrors() {
    errors.value = [];
  }

  return {
    // 상태
    currentLandingPage,
    selectedElementId,
    history,
    isEditing,
    isSaving,
    lastSaved,
    errors,

    // 게터
    selectedElement,
    canUndo,
    canRedo,

    // 액션
    createNewLandingPage,
    loadLandingPage,
    saveStateToHistory,
    undo,
    redo,
    addElement,
    selectElement,
    deleteElement,
    toggleElementVisibility,
    renameElement,
    updateElementProperty,
    updateElementPosition,
    updateElementSize,
    updateCanvasProperty,
    updatePageSettings,
    bringElementForward,
    sendElementBackward,
    bringElementToFront,
    sendElementToBack,
    resizeCanvas,
    applyTemplate,
    addError,
    clearErrors,

    // 작업 그룹화
    startGroupingOperations,
    endGroupingOperations
  };
});
