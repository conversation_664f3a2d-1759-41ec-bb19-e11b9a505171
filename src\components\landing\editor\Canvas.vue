<template>
  <div
    class="landing-editor-canvas"
    :style="canvasContainerStyle"
    @click="handleCanvasClick"
  >
    <!-- 배경 이미지 -->
    <div class="canvas-background" :style="canvasBackgroundStyle">
      <img
        v-if="backgroundImage"
        :src="backgroundImage"
        :style="backgroundImageStyle"
        class="background-image"
        alt="배경 이미지"
      />
    </div>
    <!-- 요소 렌더링 -->
    <template v-if="landingPage && landingPage.elements">
      <component
        v-for="element in sortedElements"
        :key="element.id"
        v-show="element.visible !== false"
        :is="getElementComponent(element.type)"
        :element="element"
        :is-selected="element.id === selectedElementId"
        @select="selectElement"
        @update="updateElement"
        @positionUpdate="updateElementPosition"
      />
    </template>

    <!-- 캔버스가 비어있을 때 안내 메시지 -->
    <div v-else-if="landingPage" class="empty-canvas-message">
      <p>왼쪽 패널에서 요소를 추가하세요</p>
    </div>

    <!-- 랜딩 페이지가 로드되지 않았을 때 -->
    <div v-else class="no-landing-page">
      <p>랜딩 페이지를 생성하거나 불러오세요</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useLandingEditorStore } from '@/stores/landingEditor';

// 요소 컴포넌트 동적 임포트
import TextElement from './elements/TextElement.vue';
import ImageElement from './elements/ImageElement.vue';
import ButtonElement from './elements/ButtonElement.vue';
import LinkElement from './elements/LinkElement.vue';
import DivElement from './elements/DivElement.vue';

// 스토어 접근
const landingEditorStore = useLandingEditorStore();

// 프롭스
const props = defineProps({
  zoom: {
    type: Number,
    default: 1
  }
});

// 현재 랜딩 페이지
const landingPage = computed(() => landingEditorStore.currentLandingPage);

// 선택된 요소 ID
const selectedElementId = computed(() => landingEditorStore.selectedElementId);

// 요소를 Z-인덱스 기준으로 정렬
const sortedElements = computed(() => {
  if (!landingPage.value || !landingPage.value.elements) return [];

  return [...landingPage.value.elements].sort((a, b) => a.zIndex - b.zIndex);
});

// 배경 이미지 URL
const backgroundImage = computed(() => {
  if (!landingPage.value || !landingPage.value.canvas) return null;
  return landingPage.value.canvas.backgroundImage;
});

// 캔버스 컨테이너 스타일
const canvasContainerStyle = computed(() => {
  if (!landingPage.value || !landingPage.value.canvas) return {};

  const { width, height } = landingPage.value.canvas;

  return {
    width: `${width}px`,
    height: `${height}px`,
    transform: `scale(${props.zoom})`,
    transformOrigin: 'top left'
  };
});

// 캔버스 배경 스타일
const canvasBackgroundStyle = computed(() => {
  if (!landingPage.value || !landingPage.value.canvas) return {};

  const { backgroundColor } = landingPage.value.canvas;

  return {
    backgroundColor: backgroundColor || '#ffffff',
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: -1
  };
});

// 배경 이미지 스타일
const backgroundImageStyle = computed(() => {
  if (!landingPage.value || !landingPage.value.canvas) return {};

  const { backgroundFit } = landingPage.value.canvas;

  return {
    width: '100%',
    height: '100%',
    objectFit: backgroundFit || 'cover',
    objectPosition: 'center'
  };
});

// 요소 타입에 따른 컴포넌트 반환
const getElementComponent = (type) => {
  const componentMap = {
    text: TextElement,
    image: ImageElement,
    button: ButtonElement,
    link: LinkElement,
    div: DivElement
  };

  return componentMap[type] || TextElement; // 기본값으로 TextElement 사용
};

// 캔버스 클릭 핸들러
const handleCanvasClick = (event) => {
  // 캔버스 자체를 클릭했을 때 요소 선택 해제
  if (event.target === event.currentTarget) {
    landingEditorStore.selectElement(null);
  }
};

// 요소 선택 핸들러
const selectElement = (elementId) => {
  landingEditorStore.selectElement(elementId);
};

// 요소 업데이트 핸들러
const updateElement = (elementId, propertyPath, value) => {
  landingEditorStore.updateElementProperty(elementId, propertyPath, value);
};

// 요소 위치 업데이트 핸들러
const updateElementPosition = (elementId, x, y) => {
  try {
    // 요소 찾기
    const element = landingPage.value.elements.find(el => el.id === elementId);
    if (!element) {
      console.warn('요소를 찾을 수 없음:', elementId);
      return;
    }

    // 현재 위치 가져오기
    let currentX = 0;
    let currentY = 0;
    let xUnit = 'px';
    let yUnit = 'px';

    // 현재 위치 가져오기 (객체 형태인지 확인)
    if (element.position.x && typeof element.position.x === 'object') {
      currentX = Number(element.position.x.value) || 0;
      xUnit = element.position.x.unit || 'px';
    } else {
      currentX = Number(element.position.x) || 0;
    }

    if (element.position.y && typeof element.position.y === 'object') {
      currentY = Number(element.position.y.value) || 0;
      yUnit = element.position.y.unit || 'px';
    } else {
      currentY = Number(element.position.y) || 0;
    }

    // 새 위치 값 계산
    const xValue = Number(x);
    const yValue = Number(y);

    // 값이 NaN이거나 기본값 근처로 고정되는 문제 방지
    let newXValue = !isNaN(xValue) ? xValue : currentX;
    let newYValue = !isNaN(yValue) ? yValue : currentY;

    // 기본값 근처인지 확인하는 함수
    const isNearDefaultValue = (value) => {
      // 95px ~ 105px 범위를 100px 근처로 간주
      if (value >= 95 && value <= 105) return true;

      // 45px ~ 55px 범위를 50px 근처로 간주
      if (value >= 45 && value <= 55) return true;

      return false;
    };

    // 스토어 함수 호출 - 숫자 값 그대로 전달
    landingEditorStore.updateElementPosition(elementId, newXValue, newYValue);
  } catch (error) {
    console.error('위치 업데이트 오류:', error);
  }
};
</script>

<style scoped>
.landing-editor-canvas {
  position: relative;
  background-color: #ffffff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  overflow: hidden;
}

.canvas-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.background-image {
  display: block;
  width: 100%;
  height: 100%;
}

.empty-canvas-message,
.no-landing-page {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #999;
  font-size: 18px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.03);
}

.no-landing-page {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>
